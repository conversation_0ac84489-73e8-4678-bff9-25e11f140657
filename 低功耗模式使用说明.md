# STM32 RC522 低功耗模式实现说明

## 功能概述

本项目为STM32F103 + RC522 RFID系统添加了低功耗停止模式功能，具有以下特性：

- **自动休眠**：10秒内无IC卡检测时自动进入停止模式
- **中断唤醒**：通过RC522的RQ引脚中断唤醒系统
- **系统恢复**：唤醒后自动恢复时钟、WiFi连接和服务器通信
- **状态管理**：完整的系统状态机管理

## 硬件连接

### RC522 RQ引脚连接
- **RC522 RQ引脚** → **STM32 PA2引脚**
- 配置为下降沿触发中断
- 内部上拉电阻使能

### 引脚配置
```c
// RC522 RQ引脚定义
#define RC522_IRQ_Pin GPIO_PIN_2
#define RC522_IRQ_GPIO_Port GPIOA
#define RC522_IRQ_EXTI_IRQn EXTI2_IRQn
```

## 系统状态

系统包含4个主要状态：

1. **SYSTEM_NORMAL** - 正常运行状态
2. **SYSTEM_PREPARE_SLEEP** - 准备进入休眠
3. **SYSTEM_SLEEPING** - 休眠状态
4. **SYSTEM_WAKING_UP** - 唤醒恢复状态

## 主要功能模块

### 1. 低功耗管理器 (power_manager.c/h)

#### 核心函数：
- `PowerManager_Init()` - 初始化低功耗管理器
- `PowerManager_EnterStopMode()` - 进入停止模式
- `PowerManager_WakeupRestore()` - 唤醒后系统恢复
- `PowerManager_CheckTimeout()` - 检查10秒超时
- `PowerManager_UpdateCardDetectTime()` - 更新卡片检测时间

### 2. RC522中断配置

#### 中断配置：
- 配置RC522的IRQ引脚输出
- 启用RxIRq中断（卡片检测中断）
- PA2引脚配置为下降沿触发外部中断

#### 中断处理：
```c
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
    if (GPIO_Pin == RC522_IRQ_Pin) {
        if (system_state == SYSTEM_SLEEPING) {
            wakeup_flag = 1;  // 设置唤醒标志
        }
        PowerManager_UpdateCardDetectTime();  // 更新检测时间
    }
}
```

## 工作流程

### 1. 正常运行模式
- 系统正常检测IC卡
- 每次检测到卡片时更新时间戳
- 持续监控10秒超时

### 2. 进入休眠流程
1. 10秒内无卡片检测
2. 系统状态变为 `SYSTEM_PREPARE_SLEEP`
3. 显示休眠信息
4. 关闭LCD背光和LED
5. 发送休眠通知到服务器
6. 关闭不必要的外设时钟
7. 进入停止模式

### 3. 唤醒恢复流程
1. RC522检测到卡片，RQ引脚产生中断
2. 系统从停止模式唤醒
3. 系统状态变为 `SYSTEM_WAKING_UP`
4. 重新配置系统时钟
5. 重新初始化外设
6. 恢复WiFi连接
7. 重新连接服务器
8. 系统状态恢复为 `SYSTEM_NORMAL`

## 功耗优化措施

### 进入休眠前：
- 关闭LCD背光 (PA15)
- 关闭状态LED (PB2, PC5)
- 禁用UART4时钟 (ESP32通信)
- 禁用SPI1时钟 (RC522通信)
- 使用低功耗稳压器

### 唤醒后恢复：
- 重新配置系统时钟 (72MHz)
- 重新使能外设时钟
- 重新初始化外设
- 恢复网络连接

## 使用方法

### 1. 编译和烧录
1. 确保所有文件都已添加到项目中
2. 编译项目
3. 烧录到STM32F103

### 2. 硬件连接
1. 按照引脚定义连接RC522的RQ引脚到PA2
2. 确保其他硬件连接正确

### 3. 测试步骤
1. 系统启动后正常工作
2. 10秒内不放置IC卡，观察系统进入休眠
3. 将IC卡靠近RC522，观察系统唤醒
4. 检查WiFi和服务器连接是否恢复正常

## 调试信息

系统通过UART1输出调试信息：
- 休眠准备信息
- 唤醒恢复信息
- WiFi重连状态
- 卡片检测信息

## 注意事项

1. **硬件连接**：确保RC522的RQ引脚正确连接到PA2
2. **中断优先级**：EXTI2中断优先级设置为最高
3. **时钟恢复**：唤醒后必须重新配置系统时钟
4. **网络恢复**：WiFi和服务器连接需要重新建立
5. **状态同步**：确保系统状态正确转换

## 故障排除

### 1. 无法进入休眠
- 检查10秒超时逻辑
- 确认没有持续的卡片检测

### 2. 无法唤醒
- 检查RC522 RQ引脚连接
- 确认中断配置正确
- 检查RC522中断使能

### 3. 唤醒后功能异常
- 检查时钟配置恢复
- 确认外设重新初始化
- 检查WiFi重连逻辑

## 性能指标

- **休眠电流**：约1-5mA（停止模式）
- **唤醒时间**：约100-500ms
- **WiFi重连时间**：约2-5秒
- **超时时间**：10秒（可配置）

## 扩展功能

可以根据需要添加以下功能：
1. 可配置的超时时间
2. 更多的唤醒源
3. 深度睡眠模式
4. 电池电量监控
5. 定时唤醒功能
