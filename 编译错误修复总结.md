# 编译错误修复总结

## 🔧 已修复的链接错误

### 错误1: EXTI2_IRQHandler 重复定义
**问题描述**：
```
Symbol EXTI2_IRQHandler multiply defined (by power_manager.o and stm32f1xx_it.o)
```

**解决方案**：
- ✅ 从 `power_manager.c` 中删除了 `EXTI2_IRQHandler` 函数
- ✅ 保留 `stm32f1xx_it.c` 中的原有实现
- ✅ 使用现有的中断处理框架

### 错误2: HAL_GPIO_EXTI_Callback 重复定义
**问题描述**：
```
Symbol HAL_GPIO_EXTI_Callback multiply defined (by key.o and power_manager.o)
```

**解决方案**：
- ✅ 从 `power_manager.c` 中删除了 `HAL_GPIO_EXTI_Callback` 函数
- ✅ 在 `key.c` 中的现有 `HAL_GPIO_EXTI_Callback` 函数中添加RC522中断处理
- ✅ 创建了 `PowerManager_HandleRC522Interrupt()` 函数来处理RC522中断逻辑

## 📝 修改的文件

### 1. Core/Src/power_manager.c
**修改内容**：
- 删除了重复的 `EXTI2_IRQHandler` 函数
- 删除了重复的 `HAL_GPIO_EXTI_Callback` 函数
- 添加了 `PowerManager_HandleRC522Interrupt()` 函数

**新增函数**：
```c
void PowerManager_HandleRC522Interrupt(void)
{
    if (system_state == SYSTEM_SLEEPING) {
        wakeup_flag = 1;  // 从停止模式唤醒
    }
    last_card_detect_time = HAL_GetTick();  // 更新卡片检测时间
}
```

### 2. Core/Inc/power_manager.h
**修改内容**：
- 删除了 `EXTI2_IRQHandler` 函数声明
- 添加了 `PowerManager_HandleRC522Interrupt()` 函数声明

### 3. Hardware/Key/key.c
**修改内容**：
- 添加了 `#include "power_manager.h"`
- 在 `HAL_GPIO_EXTI_Callback` 函数中添加了RC522中断处理

**修改后的函数**：
```c
void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
    if (GPIO_Pin == Key1_Pin) {
        key1_flag = 1;
    }
    else if(GPIO_Pin == Key2_Pin) {
        key2_flag = 1;
    }
    else if(GPIO_Pin == Key3_Pin) {
        key3_flag = 1;
    }
    else if(GPIO_Pin == RC522_IRQ_Pin) {
        // 处理RC522中断
        PowerManager_HandleRC522Interrupt();
    }
}
```

## 🔄 中断处理流程

### 新的中断处理流程：
1. **硬件中断发生** → PA2引脚检测到下降沿
2. **EXTI2_IRQHandler** → 由 `stm32f1xx_it.c` 中的函数处理
3. **HAL_GPIO_EXTI_IRQHandler** → HAL库标准处理
4. **HAL_GPIO_EXTI_Callback** → 由 `key.c` 中的函数处理
5. **PowerManager_HandleRC522Interrupt** → 处理RC522特定逻辑

### 中断处理逻辑：
- **正常状态**：更新卡片检测时间戳
- **休眠状态**：设置唤醒标志 + 更新时间戳

## ✅ 验证清单

### 编译验证：
- [ ] 项目编译无错误
- [ ] 链接无重复符号错误
- [ ] 所有源文件正确包含

### 功能验证：
- [ ] 按键中断功能正常
- [ ] RC522中断功能正常
- [ ] 低功耗模式进入/退出正常
- [ ] 系统状态管理正常

## 🚀 下一步操作

### 1. 重新编译项目
在Keil MDK-ARM中：
- 执行 "Rebuild All" 
- 检查编译输出无错误
- 确认链接成功

### 2. 功能测试
- 烧录程序到开发板
- 测试按键功能是否正常
- 测试RC522中断唤醒功能
- 验证低功耗模式工作

### 3. 调试验证
使用串口调试：
- 监控中断触发信息
- 检查系统状态转换
- 验证唤醒恢复流程

## 📋 测试要点

### 中断功能测试：
1. **按键中断** - 按下Key1/Key2/Key3应有相应响应
2. **RC522中断** - 卡片靠近应触发中断
3. **唤醒中断** - 休眠状态下卡片靠近应唤醒系统

### 低功耗功能测试：
1. **自动休眠** - 10秒无卡片检测后进入休眠
2. **中断唤醒** - 卡片靠近立即唤醒
3. **系统恢复** - 唤醒后功能完全恢复

## ⚠️ 注意事项

### 硬件连接：
- 确保RC522 RQ引脚连接到STM32 PA2
- 检查连接的可靠性和电气特性

### 软件配置：
- 确认GPIO中断配置正确
- 检查中断优先级设置
- 验证时钟配置恢复

### 调试建议：
- 使用示波器检查PA2引脚信号
- 通过串口监控中断触发
- 测试连续休眠唤醒循环

现在可以重新编译项目，应该不会再有链接错误了！
