# 低功耗模式测试验证步骤

## 测试环境准备

### 1. 硬件准备
- STM32F103开发板
- RC522 RFID模块
- LCD显示屏
- ESP32 WiFi模块
- IC卡片
- 杜邦线

### 2. 硬件连接验证
确保以下连接正确：
```
RC522 RQ引脚 → STM32 PA2引脚
RC522 SPI接口 → STM32 SPI1
ESP32 UART → STM32 UART4
LCD接口 → STM32相应GPIO
```

### 3. 软件准备
- Keil MDK-ARM开发环境
- ST-Link调试器
- 串口调试工具

## 功能测试步骤

### 测试1：系统正常启动
1. **操作**：上电启动系统
2. **预期结果**：
   - LCD显示启动信息
   - 串口输出："Power Manager Initialized"
   - WiFi连接成功
   - 服务器连接成功
   - 显示"System Ready"

### 测试2：IC卡检测功能
1. **操作**：将IC卡靠近RC522
2. **预期结果**：
   - LCD显示卡片ID
   - 串口输出卡片信息
   - 数据发送到服务器
   - 卡片检测时间更新

### 测试3：10秒超时进入休眠
1. **操作**：系统启动后10秒内不放置IC卡
2. **预期结果**：
   - 串口输出："Timeout detected, preparing to sleep..."
   - LCD显示："Entering Sleep..."
   - LCD背光关闭
   - LED灯关闭
   - 串口输出："System entering stop mode..."
   - 系统进入低功耗状态

### 测试4：中断唤醒功能
1. **前提**：系统已进入休眠状态
2. **操作**：将IC卡靠近RC522
3. **预期结果**：
   - 系统立即唤醒
   - 串口输出："System waking up from stop mode..."
   - LCD背光重新打开
   - 显示："System Waking Up..."

### 测试5：唤醒后系统恢复
1. **前提**：系统刚从休眠中唤醒
2. **预期结果**：
   - 时钟重新配置成功
   - 外设重新初始化
   - WiFi重新连接
   - 服务器重新连接
   - 显示："System Ready"
   - 串口输出："System wakeup complete!"

### 测试6：连续休眠唤醒测试
1. **操作**：重复进行休眠-唤醒循环
2. **预期结果**：
   - 每次都能正常进入休眠
   - 每次都能正常唤醒
   - 系统功能保持稳定

## 性能测试

### 功耗测试
1. **正常运行功耗**：
   - 使用万用表测量运行时电流
   - 记录典型值和峰值

2. **休眠功耗**：
   - 测量进入停止模式后的电流
   - 应显著低于运行时功耗

### 响应时间测试
1. **进入休眠时间**：
   - 从超时检测到完全进入休眠的时间
   - 应在1-2秒内完成

2. **唤醒响应时间**：
   - 从卡片靠近到系统完全恢复的时间
   - 应在3-5秒内完成

## 调试方法

### 1. 串口调试
连接UART1查看调试信息：
- 波特率：115200
- 数据位：8
- 停止位：1
- 校验位：无

### 2. 关键调试信息
```
正常启动：
- "Power Manager Initialized"
- "WIFI CONNECT SUCCESS"
- "Server Connected"

进入休眠：
- "Timeout detected, preparing to sleep..."
- "Preparing for sleep mode..."
- "System entering stop mode..."

唤醒恢复：
- "System waking up from stop mode..."
- "Restoring WiFi connection..."
- "WiFi and server reconnected successfully!"
- "System wakeup complete!"
```

### 3. LCD状态指示
- 正常运行：显示"System Ready"
- 准备休眠：显示"Entering Sleep..."
- 唤醒过程：显示"System Waking Up..."
- WiFi恢复：显示"Restoring WiFi..."

## 常见问题排查

### 问题1：无法进入休眠
**可能原因**：
- 持续有卡片检测
- 超时逻辑错误
- 系统状态异常

**排查方法**：
- 检查串口是否有持续的卡片检测信息
- 确认10秒计时是否正常
- 检查PowerManager_CheckTimeout()调用

### 问题2：无法唤醒
**可能原因**：
- RC522 RQ引脚连接错误
- 中断配置问题
- RC522中断未使能

**排查方法**：
- 用万用表检查PA2引脚电平变化
- 确认EXTI2中断配置
- 检查RC522中断寄存器配置

### 问题3：唤醒后功能异常
**可能原因**：
- 时钟配置未恢复
- 外设初始化失败
- WiFi连接失败

**排查方法**：
- 检查SystemClock_Config()调用
- 确认外设重新初始化
- 检查WiFi重连过程

### 问题4：系统不稳定
**可能原因**：
- 中断优先级冲突
- 内存泄漏
- 状态机错误

**排查方法**：
- 检查中断优先级设置
- 监控内存使用情况
- 跟踪系统状态变化

## 验收标准

### 基本功能
- ✅ 系统正常启动和初始化
- ✅ IC卡检测和数据传输
- ✅ 10秒超时自动休眠
- ✅ RC522中断唤醒
- ✅ 唤醒后系统完全恢复

### 性能指标
- ✅ 休眠功耗 < 10mA
- ✅ 唤醒时间 < 5秒
- ✅ 连续工作稳定性 > 24小时
- ✅ 休眠唤醒循环 > 1000次

### 用户体验
- ✅ LCD状态显示清晰
- ✅ 响应时间合理
- ✅ 操作简单直观
- ✅ 错误处理完善

## 测试报告模板

```
测试日期：____年____月____日
测试人员：________________
硬件版本：________________
软件版本：________________

测试结果：
□ 系统启动测试 - 通过/失败
□ IC卡检测测试 - 通过/失败  
□ 自动休眠测试 - 通过/失败
□ 中断唤醒测试 - 通过/失败
□ 系统恢复测试 - 通过/失败
□ 连续循环测试 - 通过/失败

性能数据：
- 正常运行功耗：____mA
- 休眠功耗：____mA
- 唤醒时间：____秒
- 连续工作时间：____小时

问题记录：
1. ________________________
2. ________________________
3. ________________________

总体评价：□ 合格 □ 不合格

备注：
_________________________________
_________________________________
```
