/*
 * @Author: Augment Agent
 * @Date: 2025-07-09
 * @Description: 低功耗管理模块，实现停止模式和唤醒恢复功能
 */

#include "main.h"
#include "gpio.h"
#include "usart.h"
#include "spi.h"
#include "rc522.h"
#include "esp32.h"
#include "bsp_LCD_ILI9341.h"
#include <string.h>
#include <stdio.h>

// 全局变量
static SystemState_t system_state = SYSTEM_NORMAL;
static uint32_t last_card_detect_time = 0;
static uint32_t sleep_timeout = 10000; // 10秒超时
static volatile uint8_t wakeup_flag = 0;
static volatile uint8_t card_detected_on_wakeup = 0;

// 外部变量声明
extern UART_HandleTypeDef huart1;
extern UART_HandleTypeDef huart4;
extern SPI_HandleTypeDef hspi1;

// 函数声明
void PowerManager_CheckWiFiConnection(void);
void SystemClock_Config(void);
void MX_UART4_Init(void);
void MX_SPI1_Init(void);

/**
 * @brief 初始化低功耗管理器
 */
void PowerManager_Init(void)
{
    system_state = SYSTEM_NORMAL;
    last_card_detect_time = HAL_GetTick();
    wakeup_flag = 0;
    
    // 配置RC522 IRQ引脚为外部中断
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    
    // 配置PA2为外部中断引脚
    GPIO_InitStruct.Pin = RC522_IRQ_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_IT_FALLING;  // 下降沿触发
    GPIO_InitStruct.Pull = GPIO_PULLUP;           // 上拉电阻
    HAL_GPIO_Init(RC522_IRQ_GPIO_Port, &GPIO_InitStruct);
    
    // 配置NVIC中断
    HAL_NVIC_SetPriority(RC522_IRQ_EXTI_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(RC522_IRQ_EXTI_IRQn);
    
    printf("Power Manager Initialized\r\n");
}

/**
 * @brief 获取当前系统状态
 */
SystemState_t PowerManager_GetState(void)
{
    return system_state;
}

/**
 * @brief 设置系统状态
 */
void PowerManager_SetState(SystemState_t state)
{
    system_state = state;
}

/**
 * @brief 更新卡片检测时间
 */
void PowerManager_UpdateCardDetectTime(void)
{
    last_card_detect_time = HAL_GetTick();
}

/**
 * @brief 检查是否超时需要进入休眠
 */
void PowerManager_CheckTimeout(void)
{
    if (system_state == SYSTEM_NORMAL) {
        uint32_t current_time = HAL_GetTick();
        if (current_time - last_card_detect_time > sleep_timeout) {
            system_state = SYSTEM_PREPARE_SLEEP;
            printf("Timeout detected, preparing to sleep...\r\n");
        }
    }
}

/**
 * @brief 准备进入睡眠模式前的处理
 */
static void PowerManager_PrepareForSleep(void)
{
    printf("Preparing for sleep mode...\r\n");

    // 显示休眠信息
    LCD_Fill(20, 150, 220, 170, BLACK);
    LCD_String(20, 150, "Entering Sleep...", 16, YELLOW, BLACK);
    HAL_Delay(1000);

    // 关闭LCD背光以节省功耗
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_15, GPIO_PIN_RESET);

    // 关闭LED以节省功耗
    HAL_GPIO_WritePin(GPIOB, GPIO_PIN_2, GPIO_PIN_SET);   // 蓝色LED关闭
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_5, GPIO_PIN_SET);   // 红色LED关闭

    // 发送休眠通知到服务器
    const char* sleep_msg = "SYSTEM_SLEEP\r\n";
    sendData(sleep_msg);
    HAL_Delay(100);

    // 睡眠模式保持外设时钟运行，不需要关闭时钟
    // 这样RC522可以继续工作，响应更快

    printf("System entering sleep mode...\r\n");
    HAL_Delay(100);  // 确保串口数据发送完成
}

/**
 * @brief 进入睡眠模式
 */
void PowerManager_EnterSleepMode(void)
{
    if (system_state == SYSTEM_PREPARE_SLEEP) {
        PowerManager_PrepareForSleep();
        system_state = SYSTEM_SLEEPING;

        // 配置唤醒源 - 确保RC522 IRQ中断可以唤醒
        HAL_NVIC_EnableIRQ(RC522_IRQ_EXTI_IRQn);

        // 进入睡眠模式（保持外设时钟运行）
        HAL_PWR_EnterSLEEPMode(PWR_MAINREGULATOR_ON, PWR_SLEEPENTRY_WFI);

        // 从这里开始是唤醒后的代码
        system_state = SYSTEM_WAKING_UP;
        wakeup_flag = 1;
    }
}

/**
 * @brief 从睡眠模式唤醒后的系统恢复
 */
void PowerManager_WakeupRestore(void)
{
    if (system_state == SYSTEM_WAKING_UP && wakeup_flag) {
        printf("System waking up from sleep mode...\r\n");

        // 睡眠模式唤醒后不需要重新配置时钟和外设
        // 只需要恢复显示和状态

        // 1. 打开LCD背光
        HAL_GPIO_WritePin(GPIOA, GPIO_PIN_15, GPIO_PIN_SET);

        // 2. 显示唤醒信息
        LCD_Fill(20, 150, 220, 170, BLACK);
        LCD_String(20, 150, "System Waking Up...", 16, GREEN, BLACK);
        HAL_Delay(500);  // 短暂显示唤醒信息

        // 3. 检查WiFi连接状态，如果需要则重连
        // 在睡眠模式下WiFi连接通常会保持
        PowerManager_CheckWiFiConnection();

        // 4. 如果是卡片触发的唤醒，立即尝试读取卡片
        if (card_detected_on_wakeup) {
            printf("Card detected during wakeup, attempting immediate read...\r\n");
            LCD_String(20, 150, "Reading Card...     ", 16, CYAN, BLACK);

            // 短暂延时确保RC522稳定
            HAL_Delay(50);

            // 立即尝试读取卡片
            PowerManager_ImmediateCardRead();

            card_detected_on_wakeup = 0; // 清除标志
        }

        // 5. 更新状态
        system_state = SYSTEM_NORMAL;
        last_card_detect_time = HAL_GetTick();
        wakeup_flag = 0;

        // 6. 显示系统就绪
        LCD_String(20, 150, "System Ready        ", 16, GREEN, BLACK);

        printf("System wakeup complete!\r\n");
    }
}

/**
 * @brief 检查并恢复WiFi连接（如果需要）
 */
void PowerManager_CheckWiFiConnection(void)
{
    printf("Checking WiFi connection status...\r\n");

    // 发送简单的AT命令检查ESP32状态
    if (ESP32_SendCmd("AT\r\n", "OK", 1000)) {
        printf("ESP32 responding normally\r\n");

        // 发送唤醒通知到服务器
        const char* wakeup_msg = "SYSTEM_WAKEUP\r\n";
        if (sendData(wakeup_msg)) {
            printf("Wakeup notification sent successfully\r\n");
        } else {
            printf("Failed to send wakeup notification, connection may be lost\r\n");
        }
    } else {
        printf("ESP32 not responding, may need reconnection\r\n");
    }
}

/**
 * @brief 立即读取卡片（唤醒时使用）
 */
void PowerManager_ImmediateCardRead(void)
{
    uint8_t card_id[5];
    uint8_t status;
    int retry_count = 0;
    const int max_retries = 5;

    // 多次尝试读取卡片，因为唤醒时可能需要几次尝试
    while (retry_count < max_retries) {
        status = MFRC522_Check(card_id);

        if (status == MI_OK) {
            // 成功读取到卡片
            printf("Immediate card read successful! ID: ");
            for (int i = 0; i < 4; i++) {
                printf("%02X ", card_id[i]);
            }
            printf("\r\n");

            // 显示卡片ID
            char id_str[20];
            sprintf(id_str, "%02X%02X%02X%02X", card_id[0], card_id[1], card_id[2], card_id[3]);
            LCD_Fill(50, 105, 220, 125, BLACK);
            LCD_String(50, 105, id_str, 16, GREEN, BLACK);

            // 发送数据到服务器
            char send_data[50];
            sprintf(send_data, "WAKEUP_CARD_ID:%s\r\n", id_str);
            sendData(send_data);

            // 选择并休眠卡片，防止重复读取
            if (MFRC522_SelectTag(card_id) > 0) {
                MFRC522_Halt();
                printf("Card halted after immediate read\r\n");
            }

            return; // 成功读取，退出函数
        }

        retry_count++;
        HAL_Delay(20); // 短暂延时后重试
    }

    printf("Immediate card read failed after %d retries\r\n", max_retries);
}

/**
 * @brief RC522中断处理函数（由现有的HAL_GPIO_EXTI_Callback调用）
 */
void PowerManager_HandleRC522Interrupt(void)
{
    if (system_state == SYSTEM_SLEEPING) {
        // 从睡眠模式唤醒
        wakeup_flag = 1;
        // 设置标志表示有卡片触发了唤醒
        card_detected_on_wakeup = 1;
    }
    // 更新卡片检测时间
    last_card_detect_time = HAL_GetTick();
}
