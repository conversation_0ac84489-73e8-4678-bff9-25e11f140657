/* USER CODE BEGIN Header */
/**
 ******************************************************************************
 * @file           : main.c
 * @brief          : Main program body
 ******************************************************************************
 * @attention
 *
 * Copyright (c) 2025 STMicroelectronics.
 * All rights reserved.
 *
 * This software is licensed under terms that can be found in the LICENSE file
 * in the root directory of this software component.
 * If no LICENSE file comes with this software, it is provided AS-IS.
 *
 ******************************************************************************
 */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "spi.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "led.h"
#include "key.h"
#include "esp32.h"
#include "wifi.h"
#include "rc522.h"
#include "bsp_LCD_ILI9341.h"
#include "power_manager.h"
#include <string.h>
#include <stdio.h>
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
extern uint8_t key1_flag;
extern uint8_t key2_flag;
extern uint8_t key3_flag;
extern uint8_t is_RecvOk;
extern char buff[1024];
extern uint16_t len;


uint8_t rxBuff[1024] = "00000000";
uint16_t rxLen;
uint8_t id;
uint8_t ip[16];
uint16_t port;

uint8_t	str[MFRC522_MAX_LEN];
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{
  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */
  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_USART1_UART_Init();
  MX_UART4_Init();
  MX_SPI1_Init();
  /* USER CODE BEGIN 2 */

	// 1. 锟斤拷锟斤拷锟斤拷始锟斤拷LCD锟斤拷锟斤拷锟矫伙拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷
	LCD_Init();
	LCD_SetDir(0);
	LCD_Fill(0, 0, 240, 320, BLACK);
	LCD_String(20, 280, "jsetc-lg 2025.07", 16, RED, BLACK);
	LCD_String(20, 50, "System Starting...", 16, GREEN, BLACK);

	// 2. 锟斤拷始锟斤拷RC522
	LCD_String(20, 75, "Init RC522...", 16, YELLOW, BLACK);
	MFRC522_Init();//RC522锟斤拷始锟斤拷
	LCD_String(20, 75, "RC522 Ready    ", 16, GREEN, BLACK);

	// 3. 锟斤拷始锟斤拷锟斤拷锟杰癸拷锟杰癸拷锟斤拷锟斤拷
	LCD_String(20, 200, "Init Power Manager...", 16, YELLOW, BLACK);
	PowerManager_Init();
	LCD_String(20, 200, "Power Manager Ready ", 16, GREEN, BLACK);

	// 3. 锟斤拷始锟斤拷锟斤拷锟绞硷拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷源锟斤拷锟斤拷锟�
	int cnt=0;
	int res=0;

	LCD_String(20, 100, "Setting AT Mode...", 16, YELLOW, BLACK);
	do{
			res=setATMode(STA_MODE);
		cnt++;
		HAL_Delay(500);  // 锟斤拷锟斤拷锟斤拷时时锟斤拷
	}while(res==0&&cnt<=1);  // 只锟斤拷锟斤拷1锟斤拷

	cnt=0;
	const char*message="AT MODE SET SUCCESS.\r\n";
	HAL_UART_Transmit(&huart1,(const uint8_t*)message,strlen(message),HAL_MAX_DELAY);
	LCD_String(20, 100, "AT Mode Ready   ", 16, GREEN, BLACK);

	LCD_String(20, 125, "Connecting WiFi...", 16, YELLOW, BLACK);
	do{
	const char *ssid="nihao";
		const char * passwd="88886666";
		res=connectWiFi(ssid,passwd);
		cnt++;
		HAL_Delay(500);  // 锟斤拷锟斤拷锟斤拷时时锟斤拷
	}while(res==0&&cnt<=1);  // 只锟斤拷锟斤拷1锟斤拷
	cnt=0;
	message="WIFI CONNECT SUCCESS.\r\n";
	HAL_UART_Transmit(&huart1,(const uint8_t*)message,strlen(message),HAL_MAX_DELAY);
	LCD_String(20, 125, "WiFi Connected  ", 16, GREEN, BLACK);

	const char*serverip="************";
	const uint16_t port=8888;

	// 锟斤拷锟矫讹拷锟斤拷锟斤拷模式
	LCD_String(20, 150, "Setting Multi Mode...", 16, YELLOW, BLACK);
	ESP32_SendCmd("AT+CIPMUX=1\r\n", "OK", 1000);

	LCD_String(20, 150, "Connecting Server...", 16, YELLOW, BLACK);
	cnt=0;  // 锟斤拷锟矫硷拷锟斤拷锟斤拷
	do{
		res=connectTcpServer(serverip,port);
		cnt++;
		HAL_Delay(500);  // 锟斤拷锟斤拷锟斤拷时时锟斤拷
	}while(res==0&&cnt<=1);  // 只锟斤拷锟斤拷1锟斤拷

	LCD_String(20, 150, "Server Connected", 16, GREEN, BLACK);

	// 4. 锟斤拷示锟斤拷锟秸撅拷锟斤拷状态
	HAL_Delay(1000);  // 锟斤拷锟矫伙拷锟斤拷锟斤拷锟斤拷锟接成癸拷锟斤拷息
	LCD_Fill(20, 50, 220, 170, BLACK);  // 锟斤拷锟斤拷锟斤拷锟斤拷锟较�
	LCD_String(20, 80, "NFC RC522 TEST", 16, RED, BLACK);
	LCD_String(20, 105, "ID:", 16, RED, BLACK);
	LCD_String(50, 105, (char *)rxBuff, 16, RED, BLACK);
	LCD_String(20, 130, "System Ready", 16, GREEN, BLACK);


  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */

 
  while (1)
  {
		// 锟斤拷锟斤拷锟斤拷锟斤拷芄锟斤拷锟阶刺�锟斤拷锟斤拷
		SystemState_t current_state = PowerManager_GetState();

		// 锟斤拷锟斤拷系统状态执锟叫诧拷同锟竭硷拷
		switch (current_state) {
			case SYSTEM_NORMAL:
				// 锟斤拷锟斤拷锟斤拷模式 - 执锟斤拷锟斤拷锟斤拷业锟斤拷锟竭硷拷
				break;

			case SYSTEM_PREPARE_SLEEP:
				// 准锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷模式
				PowerManager_EnterStopMode();
				continue;  // 锟斤拷锟斤拷锟斤拷锟斤拷循锟斤拷

			case SYSTEM_WAKING_UP:
				// 锟斤拷锟斤拷模式 - 执锟斤拷系统锟街革拷
				PowerManager_WakeupRestore();
				continue;  // 锟斤拷锟斤拷锟斤拷锟斤拷循锟斤拷

			case SYSTEM_SLEEPING:
				// 锟斤拷锟斤拷状态 - 锟斤拷应锟矫诧拷锟街达拷械锟斤拷锟斤拷锟�
				continue;

			default:
				break;
		}

		// 锟斤拷锟�10锟斤拷锟斤拷锟绞�
		PowerManager_CheckTimeout();

		// Key2锟斤拷锟斤拷锟斤拷锟� - 锟斤拷锟斤拷锟斤拷锟接凤拷锟斤拷锟斤拷
		if (Key2_Status() == Key_Press) {
			LCD_String(20, 150, "Reconnecting...     ", 16, YELLOW, BLACK);
			printf("Key2 Pressed! Reconnecting to server...\r\n");

			// 锟斤拷锟斤拷锟斤拷锟接凤拷锟斤拷锟斤拷
			const char*serverip="************";
			const uint16_t port=8888;
			int cnt = 0;
			int res = 0;

			// 锟斤拷锟矫讹拷锟斤拷锟斤拷模式
			ESP32_SendCmd("AT+CIPMUX=1\r\n", "OK", 1000);

			// 锟斤拷锟斤拷锟斤拷锟接凤拷锟斤拷锟斤拷
			do{
				res = connectTcpServer(serverip, port);
				cnt++;
				HAL_Delay(500);
			}while(res==0 && cnt<=2);  // 锟斤拷锟斤拷2锟斤拷

			if (res == 1) {
				LCD_String(20, 150, "Server Reconnected  ", 16, GREEN, BLACK);
				printf("Server reconnected successfully!\r\n");
			} else {
				LCD_String(20, 150, "Reconnect Failed    ", 16, RED, BLACK);
				printf("Server reconnection failed!\r\n");
			}

			HAL_Delay(1000);  // 锟斤拷示锟斤拷锟�1锟斤拷
			LCD_String(20, 150, "System Ready        ", 16, GREEN, BLACK);
		}

		// IC锟斤拷锟斤拷锟�
		uint8_t card_id[5];  // 锟芥储锟斤拷片ID锟侥伙拷锟斤拷锟斤拷
		uint8_t status;
     // 锟斤拷片锟斤拷锟斤拷状态锟斤拷锟斤拷
  static uint8_t card_processed = 0;  // 0=未锟斤拷锟斤拷, 1=锟窖达拷锟斤拷
  static uint8_t last_card_id[5] = {0};  // 锟芥储锟较次达拷锟斤拷目锟狡琁D锟斤拷5锟街斤拷锟斤拷锟斤拷ID锟斤拷


		status = MFRC522_Check(card_id);  // 锟斤拷锟絀C锟斤拷

		if (status == MI_OK) {
			// 锟斤拷锟斤拷欠锟斤拷锟斤拷驴锟狡�锟斤拷未锟斤拷锟斤拷目锟狡�
			uint8_t is_new_card = 0;
			if (card_processed == 0) {
				// 锟斤拷一锟轿硷拷獾斤拷锟狡�
				is_new_card = 1;
			} else {
				// 使锟斤拷MFRC522_Compare锟斤拷锟斤拷锟斤拷锟斤拷欠锟斤拷遣锟酵�锟侥匡拷片
				if (MFRC522_Compare(card_id, last_card_id) == MI_ERR) {
					// 锟斤拷片ID锟斤拷同锟斤拷锟斤拷锟铰匡拷片
					is_new_card = 1;
				}
			}

			if (is_new_card) {
				// 锟铰匡拷片锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷
				printf("IC Card Detected! ID: ");
				for (int i = 0; i < 4; i++) {
					printf("%02X ", card_id[i]);
				}
				printf("\r\n");

				// 锟斤拷锟铰匡拷片锟斤拷獾绞憋拷锟�
				PowerManager_UpdateCardDetectTime();

				// 锟斤拷锟芥当前锟斤拷片ID锟斤拷锟斤拷锟斤拷5锟街节ｏ拷
				for (int i = 0; i < 5; i++) {
					last_card_id[i] = card_id[i];
				}
				card_processed = 1;  // 锟斤拷锟轿�锟窖达拷锟斤拷

				// 选锟斤拷片锟斤拷锟斤拷锟斤拷锟斤拷锟竭ｏ拷锟斤拷止锟截革拷锟斤拷
				if (MFRC522_SelectTag(card_id) > 0) {
					// 锟斤拷锟竭匡拷片
					MFRC522_Halt();
					printf("Card halted to prevent duplicate scanning\r\n");
				}

				// 锟斤拷LCD锟斤拷锟斤拷示锟斤拷片ID
				char id_str[20];
				sprintf(id_str, "%02X%02X%02X%02X", card_id[0], card_id[1], card_id[2], card_id[3]);
				LCD_Fill(50, 105, 220, 125, BLACK);  // 锟斤拷锟街�前锟斤拷锟斤拷示
				LCD_String(50, 105, id_str, 16, GREEN, BLACK);

				// 通锟斤拷WiFi锟斤拷锟酵匡拷片ID锟斤拷锟斤拷锟斤拷锟斤拷
				char send_data[50];
				sprintf(send_data, "CARD_ID:%s\r\n", id_str);
				WIFI_TCP_SendData(0, (uint8_t*)send_data, strlen(send_data));

				// 锟斤拷时锟斤拷锟斤拷锟截革拷锟斤拷锟酵�一锟脚匡拷
				HAL_Delay(1000);
			}
			// 锟斤拷锟斤拷锟斤拷汛锟斤拷锟斤拷同一锟脚匡拷锟斤拷什么锟斤拷锟斤拷锟斤拷
		}
		else if (status == MI_ERR) {
			// 没锟叫硷拷獾斤拷锟狡�锟斤拷锟斤拷锟斤拷状态锟斤拷
			// 锟斤拷锟矫匡拷片锟斤拷锟斤拷状态锟斤拷锟斤拷锟斤拷锟斤拷锟铰匡拷片
			card_processed = 0;

			static uint32_t last_clear_time = 0;
			if (HAL_GetTick() - last_clear_time > 2000) {  // 每2锟斤拷锟斤拷锟揭伙拷锟斤拷锟绞�
				LCD_Fill(50, 105, 220, 125, BLACK);
				LCD_String(50, 105, "No Card", 16, YELLOW, BLACK);
				last_clear_time = HAL_GetTick();
			}
		}
		else {
			// 锟斤拷锟斤拷未知锟斤拷锟斤拷
			card_processed = 0;  // 锟斤拷锟斤拷状态
			printf("Unknown error: %d\r\n", status);
		}

		// 锟斤拷锟斤拷锟斤拷时锟斤拷锟斤拷锟斤拷锟斤拷锟狡碉拷锟斤拷募锟斤拷
		HAL_Delay(100);
  }
  /* USER CODE END WHILE */

  /* USER CODE BEGIN 3 */

  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.HSEPredivValue = RCC_HSE_PREDIV_DIV1;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLMUL = RCC_PLL_MUL9;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
