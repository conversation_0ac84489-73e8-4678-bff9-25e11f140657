#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F103RCT6
Mcu.Family=STM32F1
Mcu.IP0=NVIC
Mcu.IP1=RCC
Mcu.IP2=SPI1
Mcu.IP3=SYS
Mcu.IP4=UART4
Mcu.IP5=USART1
Mcu.IPNb=6
Mcu.Name=STM32F103R(C-D-E)Tx
Mcu.Package=LQFP64
Mcu.Pin0=PC14-OSC32_IN
Mcu.Pin1=PC15-OSC32_OUT
Mcu.Pin10=PA6
Mcu.Pin11=PA7
Mcu.Pin12=PC5
Mcu.Pin13=PB2
Mcu.Pin14=PA9
Mcu.Pin15=PA10
Mcu.Pin16=PA13
Mcu.Pin17=PA14
Mcu.Pin18=PC10
Mcu.Pin19=PC11
Mcu.Pin2=PD0-OSC_IN
Mcu.Pin20=VP_SYS_VS_Systick
Mcu.Pin3=PD1-OSC_OUT
Mcu.Pin4=PC2
Mcu.Pin5=PA0-WKUP
Mcu.Pin6=PA1
Mcu.Pin7=PA2
Mcu.Pin8=PA4
Mcu.Pin9=PA5
Mcu.PinsNb=21
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F103RCTx
MxCube.Version=6.10.0
MxDb.Version=DB.6.0.100
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.EXTI0_IRQn=true\:0\:0\:true\:false\:true\:true\:true\:true
NVIC.EXTI1_IRQn=true\:0\:0\:true\:false\:true\:true\:true\:true
NVIC.EXTI2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.EXTI4_IRQn=true\:0\:0\:true\:false\:true\:true\:true\:true
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.USART1_IRQn=true\:0\:0\:true\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0-WKUP.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PA0-WKUP.GPIO_Label=Key1
PA0-WKUP.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_FALLING
PA0-WKUP.GPIO_PuPd=GPIO_PULLDOWN
PA0-WKUP.Locked=true
PA0-WKUP.Signal=GPXTI0
PA1.GPIOParameters=GPIO_PuPd,GPIO_Label
PA1.GPIO_Label=Key2
PA1.GPIO_PuPd=GPIO_PULLUP
PA1.Locked=true
PA1.Signal=GPXTI1
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.GPIOParameters=GPIO_PuPd,GPIO_ModeDefaultEXTI
PA2.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_FALLING
PA2.GPIO_PuPd=GPIO_PULLUP
PA2.Locked=true
PA2.Signal=GPXTI2
PA4.GPIOParameters=GPIO_PuPd,GPIO_Label
PA4.GPIO_Label=Key3
PA4.GPIO_PuPd=GPIO_PULLUP
PA4.Locked=true
PA4.Signal=GPXTI4
PA5.Mode=Full_Duplex_Master
PA5.Signal=SPI1_SCK
PA6.Mode=Full_Duplex_Master
PA6.Signal=SPI1_MISO
PA7.Mode=Full_Duplex_Master
PA7.Signal=SPI1_MOSI
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB2.GPIOParameters=GPIO_Speed,PinState,GPIO_Label,GPIO_ModeDefaultOutputPP
PB2.GPIO_Label=B_LED
PB2.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PB2.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB2.Locked=true
PB2.PinState=GPIO_PIN_SET
PB2.Signal=GPIO_Output
PC10.Mode=Asynchronous
PC10.Signal=UART4_TX
PC11.Mode=Asynchronous
PC11.Signal=UART4_RX
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PC2.GPIOParameters=GPIO_Label
PC2.GPIO_Label=CS_NSS
PC2.Locked=true
PC2.Signal=GPIO_Output
PC5.GPIOParameters=GPIO_Speed,PinState,GPIO_Label
PC5.GPIO_Label=R_LED
PC5.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC5.Locked=true
PC5.PinState=GPIO_PIN_SET
PC5.Signal=GPIO_Output
PD0-OSC_IN.Mode=HSE-External-Oscillator
PD0-OSC_IN.Signal=RCC_OSC_IN
PD1-OSC_OUT.Mode=HSE-External-Oscillator
PD1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F103RCTx
ProjectManager.FirmwarePackage=STM32Cube FW_F1 V1.8.6
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=UART_HAL_Test.ioc
ProjectManager.ProjectName=UART_HAL_Test
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_USART1_UART_Init-USART1-false-HAL-true,4-MX_UART4_Init-UART4-false-HAL-true,5-MX_SPI1_Init-SPI1-false-HAL-true
RCC.ADCFreqValue=36000000
RCC.AHBFreq_Value=72000000
RCC.APB1CLKDivider=RCC_HCLK_DIV2
RCC.APB1Freq_Value=36000000
RCC.APB1TimFreq_Value=72000000
RCC.APB2Freq_Value=72000000
RCC.APB2TimFreq_Value=72000000
RCC.FCLKCortexFreq_Value=72000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=72000000
RCC.I2S2Freq_Value=72000000
RCC.I2S3Freq_Value=72000000
RCC.IPParameters=ADCFreqValue,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,I2S2Freq_Value,I2S3Freq_Value,MCOFreq_Value,PLLCLKFreq_Value,PLLMCOFreq_Value,PLLMUL,PLLSourceVirtual,SDIOFreq_Value,SDIOHCLKDiv2FreqValue,SYSCLKFreq_VALUE,SYSCLKSource,TimSysFreq_Value,USBFreq_Value,VCOOutput2Freq_Value
RCC.MCOFreq_Value=72000000
RCC.PLLCLKFreq_Value=72000000
RCC.PLLMCOFreq_Value=36000000
RCC.PLLMUL=RCC_PLL_MUL9
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.SDIOFreq_Value=72000000
RCC.SDIOHCLKDiv2FreqValue=36000000
RCC.SYSCLKFreq_VALUE=72000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.TimSysFreq_Value=72000000
RCC.USBFreq_Value=72000000
RCC.VCOOutput2Freq_Value=8000000
SH.GPXTI0.0=GPIO_EXTI0
SH.GPXTI0.ConfNb=1
SH.GPXTI1.0=GPIO_EXTI1
SH.GPXTI1.ConfNb=1
SH.GPXTI2.0=GPIO_EXTI2
SH.GPXTI2.ConfNb=1
SH.GPXTI4.0=GPIO_EXTI4
SH.GPXTI4.ConfNb=1
SPI1.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_64
SPI1.CalculateBaudRate=1.125 MBits/s
SPI1.Direction=SPI_DIRECTION_2LINES
SPI1.IPParameters=VirtualType,Mode,Direction,BaudRatePrescaler,CalculateBaudRate
SPI1.Mode=SPI_MODE_MASTER
SPI1.VirtualType=VM_MASTER
UART4.IPParameters=VirtualMode
UART4.VirtualMode=Asynchronous
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
board=custom
