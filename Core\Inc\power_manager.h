/*
 * @Author: Augment Agent
 * @Date: 2025-07-09
 * @Description: 低功耗管理模块头文件
 */

#ifndef __POWER_MANAGER_H
#define __POWER_MANAGER_H

#include "main.h"

// 函数声明
void PowerManager_Init(void);
void PowerManager_EnterSleepMode(void);
void PowerManager_WakeupRestore(void);
void PowerManager_CheckTimeout(void);
void PowerManager_UpdateCardDetectTime(void);
void PowerManager_CheckWiFiConnection(void);
SystemState_t PowerManager_GetState(void);
void PowerManager_SetState(SystemState_t state);

// 中断处理函数
void PowerManager_HandleRC522Interrupt(void);

#endif /* __POWER_MANAGER_H */
