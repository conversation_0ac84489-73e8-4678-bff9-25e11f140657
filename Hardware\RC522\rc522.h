// Mifare RC522 RFID Card reader 13.56 MHz
// STM32F103 RFID RC522 SPI1 / UART / USB / Keil HAL
#ifndef __RC_522_H__
#define __RC_522_H__

#include "stm32f1xx_hal.h"
#include <stdint.h>
// SPI CS define
//#define SPI_I2S_FLAG_BSY	((uint16_t)0x0080)
#define cs_reset() 					HAL_GPIO_WritePin(GPIOC, GPIO_PIN_2, GPIO_PIN_RESET)
#define cs_set() 						HAL_GPIO_WritePin(GPIOC, GPIO_PIN_2, GPIO_PIN_SET)

// RC522 RQ引脚定义 (用于中断唤醒)
#define RC522_RQ_Pin GPIO_PIN_2
#define RC522_RQ_GPIO_Port GPIOA

// Status enumeration, Used with most functions
#define MI_OK													0
#define MI_NOTAGERR										1
#define MI_ERR												2

// MFRC522 Commands
#define PCD_IDLE											0x00		// NO action; Cancel the current command
#define PCD_AUTHENT										0x0E  	// Authentication Key
#define PCD_RECEIVE										0x08   	// Receive Data
#define PCD_TRANSMIT									0x04   	// Transmit data
#define PCD_TRANSCEIVE								0x0C   	// Transmit and receive data,
#define PCD_RESETPHASE								0x0F   	// Reset
#define PCD_CALCCRC										0x03   	// CRC Calculate

// Mifare_One card command word
#define PICC_REQIDL										0x26   	// find the antenna area does not enter hibernation
#define PICC_REQALL										0x52   	// find all the cards antenna area
#define PICC_ANTICOLL									0x93   	// anti-collision
#define PICC_SElECTTAG								0x93   	// election card
#define PICC_AUTHENT1A								0x60   	// authentication key A
#define PICC_AUTHENT1B								0x61   	// authentication key B
#define PICC_READ											0x30   	// Read Block
#define PICC_WRITE										0xA0   	// write block
#define PICC_DECREMENT								0xC0   	// debit
#define PICC_INCREMENT								0xC1   	// recharge
#define PICC_RESTORE									0xC2   	// transfer block data to the buffer
#define PICC_TRANSFER									0xB0   	// save the data in the buffer
#define PICC_HALT											0x50   	// Sleep

// MFRC522 Registers
// Page 0: Command and Status
#define MFRC522_REG_RESERVED00				0x00
#define MFRC522_REG_COMMAND						0x01
#define MFRC522_REG_COMM_IE_N					0x02
#define MFRC522_REG_DIV1_EN						0x03
#define MFRC522_REG_COMM_IRQ					0x04
#define MFRC522_REG_DIV_IRQ						0x05
#define MFRC522_REG_ERROR							0x06
#define MFRC522_REG_STATUS1						0x07
#define MFRC522_REG_STATUS2						0x08
#define MFRC522_REG_FIFO_DATA					0x09
#define MFRC522_REG_FIFO_LEVEL				0x0A
#define MFRC522_REG_WATER_LEVEL				0x0B
#define MFRC522_REG_CONTROL						0x0C
#define MFRC522_REG_BIT_FRAMING				0x0D
#define MFRC522_REG_COLL							0x0E
#define MFRC522_REG_RESERVED01				0x0F
// Page 1: Command
#define MFRC522_REG_RESERVED10				0x10
#define MFRC522_REG_MODE							0x11
#define MFRC522_REG_TX_MODE						0x12
#define MFRC522_REG_RX_MODE						0x13
#define MFRC522_REG_TX_CONTROL				0x14
#define MFRC522_REG_TX_AUTO						0x15
#define MFRC522_REG_TX_SELL						0x16
#define MFRC522_REG_RX_SELL						0x17
#define MFRC522_REG_RX_THRESHOLD			0x18
#define MFRC522_REG_DEMOD							0x19
#define MFRC522_REG_RESERVED11				0x1A
#define MFRC522_REG_RESERVED12				0x1B
#define MFRC522_REG_MIFARE						0x1C
#define MFRC522_REG_RESERVED13				0x1D
#define MFRC522_REG_RESERVED14				0x1E
#define MFRC522_REG_SERIALSPEED				0x1F
// Page 2: CFG
#define MFRC522_REG_RESERVED20				0x20
#define MFRC522_REG_CRC_RESULT_M			0x21
#define MFRC522_REG_CRC_RESULT_L			0x22
#define MFRC522_REG_RESERVED21				0x23
#define MFRC522_REG_MOD_WIDTH					0x24
#define MFRC522_REG_RESERVED22				0x25
#define MFRC522_REG_RF_CFG						0x26
#define MFRC522_REG_GS_N							0x27
#define MFRC522_REG_CWGS_PREG					0x28
#define MFRC522_REG__MODGS_PREG				0x29
#define MFRC522_REG_T_MODE						0x2A
#define MFRC522_REG_T_PRESCALER				0x2B
#define MFRC522_REG_T_RELOAD_H				0x2C
#define MFRC522_REG_T_RELOAD_L				0x2D
#define MFRC522_REG_T_COUNTER_VALUE_H	0x2E
#define MFRC522_REG_T_COUNTER_VALUE_L	0x2F
// Page 3:TestRegister
#define MFRC522_REG_RESERVED30				0x30
#define MFRC522_REG_TEST_SEL1					0x31
#define MFRC522_REG_TEST_SEL2					0x32
#define MFRC522_REG_TEST_PIN_EN				0x33
#define MFRC522_REG_TEST_PIN_VALUE		0x34
#define MFRC522_REG_TEST_BUS					0x35
#define MFRC522_REG_AUTO_TEST					0x36
#define MFRC522_REG_VERSION						0x37
#define MFRC522_REG_ANALOG_TEST				0x38
#define MFRC522_REG_TEST_ADC1					0x39
#define MFRC522_REG_TEST_ADC2					0x3A
#define MFRC522_REG_TEST_ADC0					0x3B
#define MFRC522_REG_RESERVED31				0x3C
#define MFRC522_REG_RESERVED32				0x3D
#define MFRC522_REG_RESERVED33				0x3E
#define MFRC522_REG_RESERVED34				0x3F

#define MFRC522_DUMMY									0x00		// Dummy byte
#define MFRC522_MAX_LEN								16			// Buf len byte


/**
 * @brief 检测RFID卡片并获取卡片ID
 * @param id: 指向存储卡片ID的缓冲区指针（至少5字节）
 * @return 操作状态：MI_OK(0)=成功，MI_NOTAGERR(1)=无卡片，MI_ERR(2)=错误
 * @note 该函数会自动执行寻卡、防冲突和休眠操作
 */
uint8_t MFRC522_Check(uint8_t* id);

/**
 * @brief 比较两个卡片ID是否相同
 * @param CardID: 第一个卡片ID指针（5字节）
 * @param CompareID: 第二个卡片ID指针（5字节）
 * @return 比较结果：MI_OK(0)=相同，MI_ERR(2)=不同
 */
uint8_t MFRC522_Compare(uint8_t* CardID, uint8_t* CompareID);

/**
 * @brief 向MFRC522寄存器写入数据
 * @param addr: 寄存器地址
 * @param val: 要写入的数据值
 */
void MFRC522_WriteRegister(uint8_t addr, uint8_t val);

/**
 * @brief 从MFRC522寄存器读取数据
 * @param addr: 寄存器地址
 * @return 读取到的数据值
 */
uint8_t MFRC522_ReadRegister(uint8_t addr);

/**
 * @brief 设置寄存器的指定位掩码（置1）
 * @param reg: 寄存器地址
 * @param mask: 位掩码，要设置为1的位
 */
void MFRC522_SetBitMask(uint8_t reg, uint8_t mask);

/**
 * @brief 清除寄存器的指定位掩码（置0）
 * @param reg: 寄存器地址
 * @param mask: 位掩码，要清除为0的位
 */
void MFRC522_ClearBitMask(uint8_t reg, uint8_t mask);

/**
 * @brief 寻找RFID卡片
 * @param reqMode: 寻卡模式 - PICC_REQIDL(0x26)=寻找空闲卡，PICC_REQALL(0x52)=寻找所有卡
 * @param TagType: 指向存储卡片类型的缓冲区指针
 * @return 操作状态：MI_OK(0)=成功找到卡片，MI_ERR(2)=未找到卡片
 */
uint8_t MFRC522_Request(uint8_t reqMode, uint8_t* TagType);

/**
 * @brief 与RFID卡片进行数据通信的核心函数
 * @param command: 命令类型 - PCD_AUTHENT(0x0E)=认证，PCD_TRANSCEIVE(0x0C)=收发数据
 * @param sendData: 指向要发送数据的缓冲区指针
 * @param sendLen: 要发送的数据长度
 * @param backData: 指向接收数据的缓冲区指针
 * @param backLen: 指向接收数据长度的变量指针
 * @return 操作状态：MI_OK(0)=成功，MI_NOTAGERR(1)=无卡片响应，MI_ERR(2)=通信错误
 */
uint8_t MFRC522_ToCard(uint8_t command, uint8_t* sendData, uint8_t sendLen, uint8_t* backData, uint16_t* backLen);

/**
 * @brief 防冲突处理，获取卡片序列号
 * @param serNum: 指向存储卡片序列号的缓冲区指针（至少5字节）
 * @return 操作状态：MI_OK(0)=成功获取序列号，MI_ERR(2)=防冲突失败
 * @note 序列号前4字节为卡片UID，第5字节为校验字节
 */
uint8_t MFRC522_Anticoll(uint8_t* serNum);

/**
 * @brief 计算数据的CRC校验值
 * @param pIndata: 指向输入数据的缓冲区指针
 * @param len: 输入数据长度
 * @param pOutData: 指向存储CRC结果的缓冲区指针（2字节）
 * @note CRC结果：pOutData[0]=低字节，pOutData[1]=高字节
 */
void MFRC522_CalculateCRC(uint8_t* pIndata, uint8_t len, uint8_t* pOutData);

/**
 * @brief 选择指定的RFID卡片
 * @param serNum: 指向卡片序列号的缓冲区指针（5字节）
 * @return 卡片容量大小，0表示选择失败
 */
uint8_t MFRC522_SelectTag(uint8_t* serNum);

/**
 * @brief 对指定扇区进行密钥认证
 * @param authMode: 认证模式 - PICC_AUTHENT1A(0x60)=密钥A认证，PICC_AUTHENT1B(0x61)=密钥B认证
 * @param BlockAddr: 要认证的块地址
 * @param Sectorkey: 指向6字节密钥的缓冲区指针
 * @param serNum: 指向卡片序列号的缓冲区指针（4字节）
 * @return 操作状态：MI_OK(0)=认证成功，MI_ERR(2)=认证失败
 */
uint8_t MFRC522_Auth(uint8_t authMode, uint8_t BlockAddr, uint8_t* Sectorkey, uint8_t* serNum);

/**
 * @brief 从指定块地址读取16字节数据
 * @param blockAddr: 要读取的块地址（0-63）
 * @param recvData: 指向存储读取数据的缓冲区指针（至少16字节）
 * @return 操作状态：MI_OK(0)=读取成功，MI_ERR(2)=读取失败
 * @note 读取前必须先进行密钥认证
 */
uint8_t MFRC522_Read(uint8_t blockAddr, uint8_t* recvData);

/**
 * @brief 向指定块地址写入16字节数据
 * @param blockAddr: 要写入的块地址（0-63）
 * @param writeData: 指向要写入数据的缓冲区指针（16字节）
 * @return 操作状态：MI_OK(0)=写入成功，MI_ERR(2)=写入失败
 * @note 写入前必须先进行密钥认证，且不能写入制造商块和扇区尾块
 */
uint8_t MFRC522_Write(uint8_t blockAddr, uint8_t* writeData);

/**
 * @brief 初始化MFRC522芯片
 * @note 配置定时器、射频增益、接收阈值等参数，并开启天线
 */
void MFRC522_Init(void);

/**
 * @brief 复位MFRC522芯片
 * @note 将芯片恢复到初始状态
 */
void MFRC522_Reset(void);

/**
 * @brief 开启MFRC522天线
 * @note 只有天线开启后才能进行RFID通信
 */
void MFRC522_AntennaOn(void);

/**
 * @brief 关闭MFRC522天线
 * @note 关闭天线可以节省功耗
 */
void MFRC522_AntennaOff(void);

/**
 * @brief 使RFID卡片进入休眠状态
 * @note 卡片休眠后不会响应寻卡命令，直到离开射频场重新进入
 */
void MFRC522_Halt(void);

/**
 * @brief 配置RC522中断功能
 * @note 配置RC522的IRQ引脚用于低功耗唤醒
 */
void MFRC522_ConfigureInterrupt(void);

/**
 * @brief 清除RC522中断标志
 * @note 在中断处理后调用以清除中断状态
 */
void MFRC522_ClearInterrupt(void);

#endif
