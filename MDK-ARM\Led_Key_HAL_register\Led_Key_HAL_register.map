Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    startup_stm32f103xe.o(RESET) refers to startup_stm32f103xe.o(STACK) for __initial_sp
    startup_stm32f103xe.o(RESET) refers to startup_stm32f103xe.o(.text) for Reset_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.EXTI1_IRQHandler) for EXTI1_IRQHandler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.EXTI2_IRQHandler) for EXTI2_IRQHandler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.EXTI4_IRQHandler) for EXTI4_IRQHandler
    startup_stm32f103xe.o(RESET) refers to stm32f1xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f103xe.o(.text) refers to system_stm32f1xx.o(i.SystemInit) for SystemInit
    startup_stm32f103xe.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to usart.o(i.MX_UART4_Init) for MX_UART4_Init
    main.o(i.main) refers to spi.o(i.MX_SPI1_Init) for MX_SPI1_Init
    main.o(i.main) refers to bsp_lcd_ili9341.o(i.LCD_Init) for LCD_Init
    main.o(i.main) refers to bsp_lcd_ili9341.o(i.LCD_SetDir) for LCD_SetDir
    main.o(i.main) refers to bsp_lcd_ili9341.o(i.LCD_Fill) for LCD_Fill
    main.o(i.main) refers to bsp_lcd_ili9341.o(i.LCD_String) for LCD_String
    main.o(i.main) refers to rc522.o(i.MFRC522_Init) for MFRC522_Init
    main.o(i.main) refers to power_manager.o(i.PowerManager_Init) for PowerManager_Init
    main.o(i.main) refers to esp32.o(i.setATMode) for setATMode
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.main) refers to strlen.o(.text) for strlen
    main.o(i.main) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    main.o(i.main) refers to esp32.o(i.connectWiFi) for connectWiFi
    main.o(i.main) refers to esp32.o(i.ESP32_SendCmd) for ESP32_SendCmd
    main.o(i.main) refers to esp32.o(i.connectTcpServer) for connectTcpServer
    main.o(i.main) refers to power_manager.o(i.PowerManager_GetState) for PowerManager_GetState
    main.o(i.main) refers to power_manager.o(i.PowerManager_CheckTimeout) for PowerManager_CheckTimeout
    main.o(i.main) refers to key.o(i.Key2_Status) for Key2_Status
    main.o(i.main) refers to power_manager.o(i.PowerManager_EnterStopMode) for PowerManager_EnterStopMode
    main.o(i.main) refers to power_manager.o(i.PowerManager_WakeupRestore) for PowerManager_WakeupRestore
    main.o(i.main) refers to printf8.o(i.__0printf$8) for __2printf
    main.o(i.main) refers to rc522.o(i.MFRC522_Check) for MFRC522_Check
    main.o(i.main) refers to rc522.o(i.MFRC522_Compare) for MFRC522_Compare
    main.o(i.main) refers to power_manager.o(i.PowerManager_UpdateCardDetectTime) for PowerManager_UpdateCardDetectTime
    main.o(i.main) refers to rc522.o(i.MFRC522_SelectTag) for MFRC522_SelectTag
    main.o(i.main) refers to rc522.o(i.MFRC522_Halt) for MFRC522_Halt
    main.o(i.main) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    main.o(i.main) refers to wifi.o(i.WIFI_TCP_SendData) for WIFI_TCP_SendData
    main.o(i.main) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    main.o(i.main) refers to usart.o(.bss) for huart1
    main.o(i.main) refers to main.o(.data) for .data
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    gpio.o(i.MX_GPIO_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    spi.o(i.HAL_SPI_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    spi.o(i.HAL_SPI_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    spi.o(i.MX_SPI1_Init) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_Init) for HAL_SPI_Init
    spi.o(i.MX_SPI1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    spi.o(i.MX_SPI1_Init) refers to spi.o(.bss) for .bss
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) for HAL_UARTEx_ReceiveToIdle_IT
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for .bss
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.data) for .data
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_RxCpltCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    usart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for .bss
    usart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.data) for .data
    usart.o(i.MX_UART4_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_UART4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_UART4_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.fputc) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart.o(i.fputc) refers to usart.o(.bss) for .bss
    stm32f1xx_it.o(i.EXTI0_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32f1xx_it.o(i.EXTI1_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32f1xx_it.o(i.EXTI2_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32f1xx_it.o(i.EXTI4_IRQHandler) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32f1xx_it.o(i.SysTick_Handler) refers to stm32f1xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f1xx_it.o(i.USART1_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f1xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    power_manager.o(i.PowerManager_CheckTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    power_manager.o(i.PowerManager_CheckTimeout) refers to printf8.o(i.__0printf$8) for __2printf
    power_manager.o(i.PowerManager_CheckTimeout) refers to power_manager.o(.data) for .data
    power_manager.o(i.PowerManager_EnterStopMode) refers to power_manager.o(i.PowerManager_PrepareForSleep) for PowerManager_PrepareForSleep
    power_manager.o(i.PowerManager_EnterStopMode) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    power_manager.o(i.PowerManager_EnterStopMode) refers to stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) for HAL_PWR_EnterSTOPMode
    power_manager.o(i.PowerManager_EnterStopMode) refers to power_manager.o(.data) for .data
    power_manager.o(i.PowerManager_GetState) refers to power_manager.o(.data) for .data
    power_manager.o(i.PowerManager_HandleRC522Interrupt) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    power_manager.o(i.PowerManager_HandleRC522Interrupt) refers to power_manager.o(.data) for .data
    power_manager.o(i.PowerManager_Init) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    power_manager.o(i.PowerManager_Init) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    power_manager.o(i.PowerManager_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    power_manager.o(i.PowerManager_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    power_manager.o(i.PowerManager_Init) refers to printf8.o(i.__0printf$8) for __2printf
    power_manager.o(i.PowerManager_Init) refers to power_manager.o(.data) for .data
    power_manager.o(i.PowerManager_PrepareForSleep) refers to printf8.o(i.__0printf$8) for __2printf
    power_manager.o(i.PowerManager_PrepareForSleep) refers to bsp_lcd_ili9341.o(i.LCD_Fill) for LCD_Fill
    power_manager.o(i.PowerManager_PrepareForSleep) refers to bsp_lcd_ili9341.o(i.LCD_String) for LCD_String
    power_manager.o(i.PowerManager_PrepareForSleep) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    power_manager.o(i.PowerManager_PrepareForSleep) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    power_manager.o(i.PowerManager_PrepareForSleep) refers to esp32.o(i.sendData) for sendData
    power_manager.o(i.PowerManager_RestoreWiFiConnection) refers to printf8.o(i.__0printf$8) for __2printf
    power_manager.o(i.PowerManager_RestoreWiFiConnection) refers to bsp_lcd_ili9341.o(i.LCD_String) for LCD_String
    power_manager.o(i.PowerManager_RestoreWiFiConnection) refers to esp32.o(i.ESP32_Init) for ESP32_Init
    power_manager.o(i.PowerManager_RestoreWiFiConnection) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    power_manager.o(i.PowerManager_RestoreWiFiConnection) refers to esp32.o(i.setATMode) for setATMode
    power_manager.o(i.PowerManager_RestoreWiFiConnection) refers to esp32.o(i.connectWiFi) for connectWiFi
    power_manager.o(i.PowerManager_RestoreWiFiConnection) refers to esp32.o(i.ESP32_SendCmd) for ESP32_SendCmd
    power_manager.o(i.PowerManager_RestoreWiFiConnection) refers to esp32.o(i.connectTcpServer) for connectTcpServer
    power_manager.o(i.PowerManager_RestoreWiFiConnection) refers to esp32.o(i.sendData) for sendData
    power_manager.o(i.PowerManager_SetState) refers to power_manager.o(.data) for .data
    power_manager.o(i.PowerManager_UpdateCardDetectTime) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    power_manager.o(i.PowerManager_UpdateCardDetectTime) refers to power_manager.o(.data) for .data
    power_manager.o(i.PowerManager_WakeupRestore) refers to printf8.o(i.__0printf$8) for __2printf
    power_manager.o(i.PowerManager_WakeupRestore) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    power_manager.o(i.PowerManager_WakeupRestore) refers to usart.o(i.MX_UART4_Init) for MX_UART4_Init
    power_manager.o(i.PowerManager_WakeupRestore) refers to spi.o(i.MX_SPI1_Init) for MX_SPI1_Init
    power_manager.o(i.PowerManager_WakeupRestore) refers to rc522.o(i.MFRC522_Init) for MFRC522_Init
    power_manager.o(i.PowerManager_WakeupRestore) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    power_manager.o(i.PowerManager_WakeupRestore) refers to bsp_lcd_ili9341.o(i.LCD_Fill) for LCD_Fill
    power_manager.o(i.PowerManager_WakeupRestore) refers to bsp_lcd_ili9341.o(i.LCD_String) for LCD_String
    power_manager.o(i.PowerManager_WakeupRestore) refers to power_manager.o(i.PowerManager_RestoreWiFiConnection) for PowerManager_RestoreWiFiConnection
    power_manager.o(i.PowerManager_WakeupRestore) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    power_manager.o(i.PowerManager_WakeupRestore) refers to power_manager.o(.data) for .data
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f1xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort) refers to stm32f1xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_spi.o(i.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_spi.o(i.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_spi.o(i.SPI_DMATxAbortCallback) for SPI_DMATxAbortCallback
    stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT) refers to stm32f1xx_hal_spi.o(i.SPI_DMARxAbortCallback) for SPI_DMARxAbortCallback
    stm32f1xx_hal_spi.o(i.HAL_SPI_DMAStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_spi.o(i.HAL_SPI_DeInit) refers to spi.o(i.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32f1xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.HAL_SPI_IRQHandler) refers to stm32f1xx_hal_spi.o(i.SPI_DMAAbortOnError) for SPI_DMAAbortOnError
    stm32f1xx_hal_spi.o(i.HAL_SPI_Init) refers to spi.o(i.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) for HAL_SPI_TransmitReceive_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT) refers to stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_DMA) refers to stm32f1xx_hal_spi.o(i.SPI_DMAError) for SPI_DMAError
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f1xx_hal_spi.o(i.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_IT) refers to stm32f1xx_hal_spi.o(i.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_16BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_8BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_16BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_8BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_AbortRx_ISR) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.SPI_DMAAbortOnError) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAError) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTransaction) for SPI_EndRxTransaction
    stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.SPI_DMARxAbortCallback) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f1xx_hal_spi.o(i.SPI_DMATxAbortCallback) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_spi.o(i.SPI_EndRxTransaction) refers to stm32f1xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction) refers to stm32f1xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f1xx_hal_spi.o(i.SPI_RxISR_16BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f1xx_hal_spi.o(i.SPI_RxISR_8BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f1xx_hal_spi.o(i.SPI_TxISR_16BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_TxISR_8BIT) refers to stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f1xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal.o(i.HAL_DeInit) refers to stm32f1xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(i.HAL_Delay) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_IncTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_Init) refers to stm32f1xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal.o(i.HAL_InitTick) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal.o(i.HAL_InitTick) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(i.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.data) for .data
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.constdata) for AHBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.data) for uwTickPrio
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.constdata) for APBPrescTable
    stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to stm32f1xx_hal_rcc.o(.constdata) for .constdata
    stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc_ex.o(.constdata) for .constdata
    stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to key.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe) for PWR_OverloadWfe
    stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to llushr.o(.text) for __aeabi_llsr
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.bss) for .bss
    stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.bss) for pFlash
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f1xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f1xx_hal_uart.o(i.UART_DMAError) refers to stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(i.UART_Receive_IT) refers to usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_uart.o(i.UART_SetConfig) refers to stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.data) for .data
    system_stm32f1xx.o(i.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.constdata) for .constdata
    led.o(i.LED_Loop) refers to led.o(i.LED_Trun_ALL_Off) for LED_Trun_ALL_Off
    led.o(i.LED_Loop) refers to led.o(i.LED_Trun_On) for LED_Trun_On
    led.o(i.LED_Loop) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    led.o(i.LED_Loop) refers to led.o(i.LED_Trun_Off) for LED_Trun_Off
    led.o(i.LED_Toggle) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    led.o(i.LED_Trun_ALL_Off) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    led.o(i.LED_Trun_ALL_On) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    led.o(i.LED_Trun_Off) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    led.o(i.LED_Trun_On) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    key.o(i.HAL_GPIO_EXTI_Callback) refers to power_manager.o(i.PowerManager_HandleRC522Interrupt) for PowerManager_HandleRC522Interrupt
    key.o(i.HAL_GPIO_EXTI_Callback) refers to key.o(.data) for .data
    key.o(i.Key1_Status) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key.o(i.Key2_Status) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    key.o(i.Key3_Status) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    bsp_lcd_ili9341.o(i.LCD_Circle) refers to bsp_lcd_ili9341.o(i.LCD_DrawPoint) for LCD_DrawPoint
    bsp_lcd_ili9341.o(i.LCD_Cross) refers to bsp_lcd_ili9341.o(i.LCD_Line) for LCD_Line
    bsp_lcd_ili9341.o(i.LCD_DispFlush) refers to bsp_lcd_ili9341.o(i.setCursor) for setCursor
    bsp_lcd_ili9341.o(i.LCD_DispFlush) refers to bsp_lcd_ili9341.o(i.sendDataShort) for sendDataShort
    bsp_lcd_ili9341.o(i.LCD_DisplayOff) refers to bsp_lcd_ili9341.o(i.sendOrder) for sendOrder
    bsp_lcd_ili9341.o(i.LCD_DisplayOn) refers to bsp_lcd_ili9341.o(i.sendOrder) for sendOrder
    bsp_lcd_ili9341.o(i.LCD_Fill) refers to bsp_lcd_ili9341.o(i.setCursor) for setCursor
    bsp_lcd_ili9341.o(i.LCD_GUI) refers to bsp_lcd_ili9341.o(i.LCD_Fill) for LCD_Fill
    bsp_lcd_ili9341.o(i.LCD_GUI) refers to bsp_lcd_ili9341.o(i.LCD_String) for LCD_String
    bsp_lcd_ili9341.o(i.LCD_GUI) refers to bsp_lcd_ili9341.o(i.LCD_Line) for LCD_Line
    bsp_lcd_ili9341.o(i.LCD_GUI) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    bsp_lcd_ili9341.o(i.LCD_GUI) refers to bsp_lcd_ili9341.o(.bss) for .bss
    bsp_lcd_ili9341.o(i.LCD_GUI) refers to system_stm32f1xx.o(.data) for SystemCoreClock
    bsp_lcd_ili9341.o(i.LCD_GetDir) refers to bsp_lcd_ili9341.o(.bss) for .bss
    bsp_lcd_ili9341.o(i.LCD_GetHeight) refers to bsp_lcd_ili9341.o(.bss) for .bss
    bsp_lcd_ili9341.o(i.LCD_GetWidth) refers to bsp_lcd_ili9341.o(.bss) for .bss
    bsp_lcd_ili9341.o(i.LCD_Image) refers to bsp_lcd_ili9341.o(i.setCursor) for setCursor
    bsp_lcd_ili9341.o(i.LCD_Image) refers to bsp_lcd_ili9341.o(i.sendDataShort) for sendDataShort
    bsp_lcd_ili9341.o(i.LCD_Init) refers to bsp_lcd_ili9341.o(i.delay_ms) for delay_ms
    bsp_lcd_ili9341.o(i.LCD_Init) refers to bsp_lcd_ili9341.o(i.sendOrder) for sendOrder
    bsp_lcd_ili9341.o(i.LCD_Init) refers to bsp_lcd_ili9341.o(i.readData) for readData
    bsp_lcd_ili9341.o(i.LCD_Init) refers to bsp_lcd_ili9341.o(i.sendDataShort) for sendDataShort
    bsp_lcd_ili9341.o(i.LCD_Init) refers to bsp_lcd_ili9341.o(i.LCD_SetDir) for LCD_SetDir
    bsp_lcd_ili9341.o(i.LCD_Init) refers to bsp_lcd_ili9341.o(i.LCD_Fill) for LCD_Fill
    bsp_lcd_ili9341.o(i.LCD_Init) refers to bsp_lcd_ili9341.o(.bss) for .bss
    bsp_lcd_ili9341.o(i.LCD_Line) refers to bsp_lcd_ili9341.o(i.LCD_DrawPoint) for LCD_DrawPoint
    bsp_lcd_ili9341.o(i.LCD_ReadPoint) refers to bsp_lcd_ili9341.o(i.setCursor) for setCursor
    bsp_lcd_ili9341.o(i.LCD_ReadPoint) refers to bsp_lcd_ili9341.o(i.sendOrder) for sendOrder
    bsp_lcd_ili9341.o(i.LCD_ReadPoint) refers to bsp_lcd_ili9341.o(.bss) for .bss
    bsp_lcd_ili9341.o(i.LCD_Rectangle) refers to bsp_lcd_ili9341.o(i.LCD_Line) for LCD_Line
    bsp_lcd_ili9341.o(i.LCD_SetDir) refers to bsp_lcd_ili9341.o(i.sendOrder) for sendOrder
    bsp_lcd_ili9341.o(i.LCD_SetDir) refers to bsp_lcd_ili9341.o(i.sendDataShort) for sendDataShort
    bsp_lcd_ili9341.o(i.LCD_SetDir) refers to bsp_lcd_ili9341.o(.bss) for .bss
    bsp_lcd_ili9341.o(i.LCD_ShowChinese) refers to bsp_lcd_ili9341.o(i.LCD_DrawPoint) for LCD_DrawPoint
    bsp_lcd_ili9341.o(i.LCD_ShowChinese) refers to bsp_lcd_ili9341.o(.constdata) for .constdata
    bsp_lcd_ili9341.o(i.LCD_ShowChinese) refers to bsp_lcd_ili9341.o(.data) for .data
    bsp_lcd_ili9341.o(i.LCD_String) refers to bsp_lcd_ili9341.o(i.drawAscii) for drawAscii
    bsp_lcd_ili9341.o(i.LCD_String) refers to bsp_lcd_ili9341.o(i.String_Index) for String_Index
    bsp_lcd_ili9341.o(i.LCD_String) refers to bsp_lcd_ili9341.o(i.LCD_ShowChinese) for LCD_ShowChinese
    bsp_lcd_ili9341.o(i.LCD_String) refers to bsp_lcd_ili9341.o(.bss) for .bss
    bsp_lcd_ili9341.o(i.String_Index) refers to bsp_lcd_ili9341.o(.data) for .data
    bsp_lcd_ili9341.o(i.delay_ms) refers to bsp_lcd_ili9341.o(.data) for .data
    bsp_lcd_ili9341.o(i.drawAscii) refers to bsp_lcd_ili9341.o(i.LCD_DrawPoint) for LCD_DrawPoint
    bsp_lcd_ili9341.o(i.drawAscii) refers to bsp_lcd_ili9341.o(.data) for .data
    bsp_lcd_ili9341.o(i.drawAscii) refers to bsp_lcd_ili9341.o(.bss) for .bss
    bsp_lcd_ili9341.o(i.drawAscii) refers to bsp_lcd_ili9341.o(.constdata) for .constdata
    bsp_lcd_ili9341.o(i.readReg) refers to bsp_lcd_ili9341.o(i.sendOrder) for sendOrder
    bsp_lcd_ili9341.o(i.readReg) refers to bsp_lcd_ili9341.o(i.readData) for readData
    esp32.o(i.ESP32_Init) refers to usart.o(i.MX_UART4_Init) for MX_UART4_Init
    esp32.o(i.ESP32_Init) refers to esp32.o(i.ESP32_SendCmd) for ESP32_SendCmd
    esp32.o(i.ESP32_Init) refers to stm32f1xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp32.o(i.ESP32_ReadResp) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) for HAL_UARTEx_ReceiveToIdle
    esp32.o(i.ESP32_ReadResp) refers to usart.o(.bss) for huart4
    esp32.o(i.ESP32_SendCmd) refers to strlen.o(.text) for strlen
    esp32.o(i.ESP32_SendCmd) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    esp32.o(i.ESP32_SendCmd) refers to memseta.o(.text) for __aeabi_memclr4
    esp32.o(i.ESP32_SendCmd) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Receive) for HAL_UART_Receive
    esp32.o(i.ESP32_SendCmd) refers to strstr.o(.text) for strstr
    esp32.o(i.ESP32_SendCmd) refers to usart.o(.bss) for huart4
    esp32.o(i.connectTcpServer) refers to memseta.o(.text) for __aeabi_memclr4
    esp32.o(i.connectTcpServer) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    esp32.o(i.connectTcpServer) refers to esp32.o(i.ESP32_SendCmd) for ESP32_SendCmd
    esp32.o(i.connectWiFi) refers to memseta.o(.text) for __aeabi_memclr4
    esp32.o(i.connectWiFi) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    esp32.o(i.connectWiFi) refers to esp32.o(i.ESP32_SendCmd) for ESP32_SendCmd
    esp32.o(i.sendData) refers to memseta.o(.text) for __aeabi_memclr4
    esp32.o(i.sendData) refers to strlen.o(.text) for strlen
    esp32.o(i.sendData) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    esp32.o(i.sendData) refers to esp32.o(i.ESP32_SendCmd) for ESP32_SendCmd
    esp32.o(i.sendData) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    esp32.o(i.sendData) refers to usart.o(.bss) for huart4
    esp32.o(i.setATMode) refers to memseta.o(.text) for __aeabi_memclr4
    esp32.o(i.setATMode) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    esp32.o(i.setATMode) refers to esp32.o(i.ESP32_SendCmd) for ESP32_SendCmd
    wifi.o(i.WIFI_AP_Mode) refers to printf8.o(i.__0printf$8) for __2printf
    wifi.o(i.WIFI_AP_Mode) refers to esp32.o(i.ESP32_SendCmd) for ESP32_SendCmd
    wifi.o(i.WIFI_Init) refers to esp32.o(i.ESP32_Init) for ESP32_Init
    wifi.o(i.WIFI_Init) refers to wifi.o(i.WIFI_STA_Mode) for WIFI_STA_Mode
    wifi.o(i.WIFI_Init) refers to wifi.o(i.WIFI_AP_Mode) for WIFI_AP_Mode
    wifi.o(i.WIFI_Init) refers to printf8.o(i.__0printf$8) for __2printf
    wifi.o(i.WIFI_STA_Mode) refers to printf8.o(i.__0printf$8) for __2printf
    wifi.o(i.WIFI_STA_Mode) refers to esp32.o(i.ESP32_SendCmd) for ESP32_SendCmd
    wifi.o(i.WIFI_STA_Mode) refers to wifi.o(i.WIFI_AP_Mode) for i.WIFI_AP_Mode
    wifi.o(i.WIFI_TCP_ReadData) refers to _scanf_int.o(.text) for _scanf_int
    wifi.o(i.WIFI_TCP_ReadData) refers to _scanf_str.o(.text) for _scanf_string
    wifi.o(i.WIFI_TCP_ReadData) refers to stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) for HAL_UARTEx_ReceiveToIdle
    wifi.o(i.WIFI_TCP_ReadData) refers to strstr.o(.text) for strstr
    wifi.o(i.WIFI_TCP_ReadData) refers to __0sscanf.o(.text) for __0sscanf
    wifi.o(i.WIFI_TCP_ReadData) refers to memcpya.o(.text) for __aeabi_memcpy
    wifi.o(i.WIFI_TCP_ReadData) refers to wifi.o(.data) for .data
    wifi.o(i.WIFI_TCP_ReadData) refers to wifi.o(.bss) for .bss
    wifi.o(i.WIFI_TCP_ReadData) refers to usart.o(.bss) for huart4
    wifi.o(i.WIFI_TCP_SendData) refers to memseta.o(.text) for __aeabi_memclr4
    wifi.o(i.WIFI_TCP_SendData) refers to printf8.o(i.__0sprintf$8) for __2sprintf
    wifi.o(i.WIFI_TCP_SendData) refers to esp32.o(i.ESP32_SendCmd) for ESP32_SendCmd
    wifi.o(i.WIFI_TCP_SendData) refers to stm32f1xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    wifi.o(i.WIFI_TCP_SendData) refers to usart.o(.bss) for huart4
    wifi.o(i.WIFI_TCP_ServerStart) refers to printf8.o(i.__0printf$8) for __2printf
    wifi.o(i.WIFI_TCP_ServerStart) refers to esp32.o(i.ESP32_SendCmd) for ESP32_SendCmd
    rc522.o(i.MFRC522_AntennaOff) refers to rc522.o(i.MFRC522_ClearBitMask) for MFRC522_ClearBitMask
    rc522.o(i.MFRC522_AntennaOn) refers to rc522.o(i.MFRC522_ReadRegister) for MFRC522_ReadRegister
    rc522.o(i.MFRC522_AntennaOn) refers to rc522.o(i.MFRC522_SetBitMask) for MFRC522_SetBitMask
    rc522.o(i.MFRC522_Anticoll) refers to rc522.o(i.MFRC522_WriteRegister) for MFRC522_WriteRegister
    rc522.o(i.MFRC522_Anticoll) refers to rc522.o(i.MFRC522_ToCard) for MFRC522_ToCard
    rc522.o(i.MFRC522_Auth) refers to rc522.o(i.MFRC522_ToCard) for MFRC522_ToCard
    rc522.o(i.MFRC522_Auth) refers to rc522.o(i.MFRC522_ReadRegister) for MFRC522_ReadRegister
    rc522.o(i.MFRC522_CalculateCRC) refers to rc522.o(i.MFRC522_ClearBitMask) for MFRC522_ClearBitMask
    rc522.o(i.MFRC522_CalculateCRC) refers to rc522.o(i.MFRC522_SetBitMask) for MFRC522_SetBitMask
    rc522.o(i.MFRC522_CalculateCRC) refers to rc522.o(i.MFRC522_WriteRegister) for MFRC522_WriteRegister
    rc522.o(i.MFRC522_CalculateCRC) refers to rc522.o(i.MFRC522_ReadRegister) for MFRC522_ReadRegister
    rc522.o(i.MFRC522_Check) refers to rc522.o(i.MFRC522_Request) for MFRC522_Request
    rc522.o(i.MFRC522_Check) refers to rc522.o(i.MFRC522_Anticoll) for MFRC522_Anticoll
    rc522.o(i.MFRC522_Check) refers to rc522.o(i.MFRC522_Halt) for MFRC522_Halt
    rc522.o(i.MFRC522_ClearBitMask) refers to rc522.o(i.MFRC522_ReadRegister) for MFRC522_ReadRegister
    rc522.o(i.MFRC522_ClearBitMask) refers to rc522.o(i.MFRC522_WriteRegister) for MFRC522_WriteRegister
    rc522.o(i.MFRC522_ClearInterrupt) refers to rc522.o(i.MFRC522_ReadRegister) for MFRC522_ReadRegister
    rc522.o(i.MFRC522_ClearInterrupt) refers to rc522.o(i.MFRC522_WriteRegister) for MFRC522_WriteRegister
    rc522.o(i.MFRC522_ConfigureInterrupt) refers to rc522.o(i.MFRC522_WriteRegister) for MFRC522_WriteRegister
    rc522.o(i.MFRC522_Halt) refers to rc522.o(i.MFRC522_CalculateCRC) for MFRC522_CalculateCRC
    rc522.o(i.MFRC522_Halt) refers to rc522.o(i.MFRC522_ToCard) for MFRC522_ToCard
    rc522.o(i.MFRC522_Init) refers to rc522.o(i.MFRC522_Reset) for MFRC522_Reset
    rc522.o(i.MFRC522_Init) refers to rc522.o(i.MFRC522_WriteRegister) for MFRC522_WriteRegister
    rc522.o(i.MFRC522_Init) refers to rc522.o(i.MFRC522_AntennaOn) for MFRC522_AntennaOn
    rc522.o(i.MFRC522_Read) refers to rc522.o(i.MFRC522_CalculateCRC) for MFRC522_CalculateCRC
    rc522.o(i.MFRC522_Read) refers to rc522.o(i.MFRC522_ToCard) for MFRC522_ToCard
    rc522.o(i.MFRC522_ReadRegister) refers to rc522.o(i.SPI1_ReadReg) for SPI1_ReadReg
    rc522.o(i.MFRC522_Request) refers to rc522.o(i.MFRC522_WriteRegister) for MFRC522_WriteRegister
    rc522.o(i.MFRC522_Request) refers to rc522.o(i.MFRC522_ToCard) for MFRC522_ToCard
    rc522.o(i.MFRC522_Reset) refers to rc522.o(i.MFRC522_WriteRegister) for MFRC522_WriteRegister
    rc522.o(i.MFRC522_SelectTag) refers to rc522.o(i.MFRC522_CalculateCRC) for MFRC522_CalculateCRC
    rc522.o(i.MFRC522_SelectTag) refers to rc522.o(i.MFRC522_ToCard) for MFRC522_ToCard
    rc522.o(i.MFRC522_SetBitMask) refers to rc522.o(i.MFRC522_ReadRegister) for MFRC522_ReadRegister
    rc522.o(i.MFRC522_SetBitMask) refers to rc522.o(i.MFRC522_WriteRegister) for MFRC522_WriteRegister
    rc522.o(i.MFRC522_ToCard) refers to rc522.o(i.MFRC522_WriteRegister) for MFRC522_WriteRegister
    rc522.o(i.MFRC522_ToCard) refers to rc522.o(i.MFRC522_ClearBitMask) for MFRC522_ClearBitMask
    rc522.o(i.MFRC522_ToCard) refers to rc522.o(i.MFRC522_SetBitMask) for MFRC522_SetBitMask
    rc522.o(i.MFRC522_ToCard) refers to rc522.o(i.MFRC522_ReadRegister) for MFRC522_ReadRegister
    rc522.o(i.MFRC522_Write) refers to rc522.o(i.MFRC522_CalculateCRC) for MFRC522_CalculateCRC
    rc522.o(i.MFRC522_Write) refers to rc522.o(i.MFRC522_ToCard) for MFRC522_ToCard
    rc522.o(i.MFRC522_WriteRegister) refers to rc522.o(i.SPI1_WriteReg) for SPI1_WriteReg
    rc522.o(i.SPI1SendByte) refers to stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    rc522.o(i.SPI1SendByte) refers to spi.o(.bss) for hspi1
    rc522.o(i.SPI1_ReadReg) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    rc522.o(i.SPI1_ReadReg) refers to rc522.o(i.SPI1SendByte) for SPI1SendByte
    rc522.o(i.SPI1_WriteReg) refers to stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    rc522.o(i.SPI1_WriteReg) refers to rc522.o(i.SPI1SendByte) for SPI1SendByte
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f103xe.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f103xe.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace_c.o(.text) for isspace
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    isspace_c.o(.text) refers to ctype_c.o(.text) for __ctype_lookup
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    _scanf.o(.text) refers (Weak) to _scanf_str.o(.text) for _scanf_string
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    ctype_c.o(.text) refers to ctype_c.o(.constdata) for .constdata


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f103xe.o(HEAP), (512 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(.bss), (16 bytes).
    Removing main.o(.bss), (16 bytes).
    Removing main.o(.data), (2 bytes).
    Removing main.o(.data), (1 bytes).
    Removing main.o(.data), (2 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing spi.o(.rev16_text), (4 bytes).
    Removing spi.o(.revsh_text), (4 bytes).
    Removing spi.o(.rrx_text), (6 bytes).
    Removing spi.o(i.HAL_SPI_MspDeInit), (40 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (92 bytes).
    Removing stm32f1xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing power_manager.o(.rev16_text), (4 bytes).
    Removing power_manager.o(.revsh_text), (4 bytes).
    Removing power_manager.o(.rrx_text), (6 bytes).
    Removing power_manager.o(i.PowerManager_SetState), (12 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_ConfigEventout), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_DisableEventout), (16 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(i.HAL_GPIOEx_EnableEventout), (16 bytes).
    Removing stm32f1xx_hal_spi.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_spi.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_spi.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Abort), (288 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Abort_IT), (288 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_DMAPause), (38 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_DMAResume), (38 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_DMAStop), (66 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_DeInit), (46 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_GetError), (4 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_GetState), (6 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_IRQHandler), (224 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_MspInit), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Receive), (340 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_DMA), (228 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Receive_IT), (168 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_RxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit), (358 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_DMA), (272 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive_IT), (156 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_DMA), (204 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_Transmit_IT), (144 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TxRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.HAL_SPI_TxRxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_16BIT), (48 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_2linesRxISR_8BIT), (48 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_16BIT), (48 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_2linesTxISR_8BIT), (48 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_AbortRx_ISR), (88 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_AbortTx_ISR), (28 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_CloseRxTx_ISR), (144 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_CloseRx_ISR), (76 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_CloseTx_ISR), (124 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAAbortOnError), (16 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAError), (34 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAHalfReceiveCplt), (10 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitCplt), (10 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAHalfTransmitReceiveCplt), (10 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMAReceiveCplt), (106 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMARxAbortCallback), (98 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMATransmitCplt), (100 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMATransmitReceiveCplt), (90 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_DMATxAbortCallback), (112 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_EndRxTransaction), (92 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_RxISR_16BIT), (32 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_RxISR_8BIT), (32 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_TxISR_16BIT), (32 bytes).
    Removing stm32f1xx_hal_spi.o(i.SPI_TxISR_8BIT), (32 bytes).
    Removing stm32f1xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_DeInit), (32 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f1xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f1xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f1xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DeInit), (220 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (144 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (72 bytes).
    Removing stm32f1xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (24 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (184 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (236 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.constdata), (18 bytes).
    Removing stm32f1xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_DeInit), (316 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f1xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f1xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.DMA_SetConfig), (42 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_DeInit), (128 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_IRQHandler), (584 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Init), (124 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (988 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (74 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start), (80 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_Start_IT), (112 bytes).
    Removing stm32f1xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (28 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f1xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f1xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_Program_HalfWord), (28 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_SetErrorCode), (92 bytes).
    Removing stm32f1xx_hal_flash.o(i.FLASH_WaitForLastOperation), (84 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (264 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (4 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (36 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program), (128 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Program_IT), (80 bytes).
    Removing stm32f1xx_hal_flash.o(i.HAL_FLASH_Unlock), (40 bytes).
    Removing stm32f1xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_MassErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (176 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (24 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (100 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.FLASH_PageErase), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (168 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (72 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBErase), (84 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (36 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetUserData), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (200 bytes).
    Removing stm32f1xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (104 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (140 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f1xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (164 bytes).
    Removing stm32f1xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (74 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DMAStop), (112 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (120 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f1xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMAError), (74 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMAReceiveCplt), (134 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxHalfCplt), (30 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_EndTxTransfer), (28 bytes).
    Removing stm32f1xx_hal_uart.o(i.UART_Start_Receive_DMA), (144 bytes).
    Removing system_stm32f1xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f1xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f1xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f1xx.o(i.SystemCoreClockUpdate), (104 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing led.o(.rrx_text), (6 bytes).
    Removing led.o(i.LED_Loop), (68 bytes).
    Removing led.o(i.LED_Toggle), (32 bytes).
    Removing led.o(i.LED_Trun_ALL_Off), (36 bytes).
    Removing led.o(i.LED_Trun_ALL_On), (36 bytes).
    Removing led.o(i.LED_Trun_Off), (40 bytes).
    Removing led.o(i.LED_Trun_On), (40 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing key.o(i.Key1_Status), (40 bytes).
    Removing key.o(i.Key3_Status), (40 bytes).
    Removing bsp_lcd_ili9341.o(.rev16_text), (4 bytes).
    Removing bsp_lcd_ili9341.o(.revsh_text), (4 bytes).
    Removing bsp_lcd_ili9341.o(.rrx_text), (6 bytes).
    Removing bsp_lcd_ili9341.o(i.LCD_BGR2RGB), (18 bytes).
    Removing bsp_lcd_ili9341.o(i.LCD_Circle), (196 bytes).
    Removing bsp_lcd_ili9341.o(i.LCD_Cross), (46 bytes).
    Removing bsp_lcd_ili9341.o(i.LCD_DispFlush), (60 bytes).
    Removing bsp_lcd_ili9341.o(i.LCD_DisplayOff), (24 bytes).
    Removing bsp_lcd_ili9341.o(i.LCD_DisplayOn), (24 bytes).
    Removing bsp_lcd_ili9341.o(i.LCD_GUI), (952 bytes).
    Removing bsp_lcd_ili9341.o(i.LCD_GetDir), (12 bytes).
    Removing bsp_lcd_ili9341.o(i.LCD_GetHeight), (12 bytes).
    Removing bsp_lcd_ili9341.o(i.LCD_GetWidth), (12 bytes).
    Removing bsp_lcd_ili9341.o(i.LCD_Image), (84 bytes).
    Removing bsp_lcd_ili9341.o(i.LCD_Line), (156 bytes).
    Removing bsp_lcd_ili9341.o(i.LCD_ReadPoint), (188 bytes).
    Removing bsp_lcd_ili9341.o(i.LCD_Rectangle), (68 bytes).
    Removing bsp_lcd_ili9341.o(i.opt_delay), (8 bytes).
    Removing bsp_lcd_ili9341.o(i.readReg), (14 bytes).
    Removing bsp_lcd_ili9341.o(.constdata), (160 bytes).
    Removing esp32.o(.rev16_text), (4 bytes).
    Removing esp32.o(.revsh_text), (4 bytes).
    Removing esp32.o(.rrx_text), (6 bytes).
    Removing esp32.o(i.ESP32_ReadResp), (28 bytes).
    Removing esp32.o(.bss), (1024 bytes).
    Removing esp32.o(.data), (2 bytes).
    Removing wifi.o(.rev16_text), (4 bytes).
    Removing wifi.o(.revsh_text), (4 bytes).
    Removing wifi.o(.rrx_text), (6 bytes).
    Removing wifi.o(i.WIFI_AP_Mode), (200 bytes).
    Removing wifi.o(i.WIFI_Init), (60 bytes).
    Removing wifi.o(i.WIFI_STA_Mode), (184 bytes).
    Removing wifi.o(i.WIFI_TCP_ReadData), (148 bytes).
    Removing wifi.o(i.WIFI_TCP_ServerStart), (180 bytes).
    Removing wifi.o(.bss), (1024 bytes).
    Removing wifi.o(.data), (2 bytes).
    Removing rc522.o(.rev16_text), (4 bytes).
    Removing rc522.o(.revsh_text), (4 bytes).
    Removing rc522.o(.rrx_text), (6 bytes).
    Removing rc522.o(i.MFRC522_AntennaOff), (8 bytes).
    Removing rc522.o(i.MFRC522_Auth), (82 bytes).
    Removing rc522.o(i.MFRC522_ClearInterrupt), (38 bytes).
    Removing rc522.o(i.MFRC522_ConfigureInterrupt), (22 bytes).
    Removing rc522.o(i.MFRC522_Read), (50 bytes).
    Removing rc522.o(i.MFRC522_Write), (132 bytes).
    Removing dadd.o(.text), (334 bytes).
    Removing dmul.o(.text), (228 bytes).
    Removing ddiv.o(.text), (222 bytes).
    Removing dfixul.o(.text), (48 bytes).
    Removing cdrcmple.o(.text), (48 bytes).
    Removing depilogue.o(.text), (186 bytes).

355 unused section(s) (total 22595 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/power_manager.c              0x00000000   Number         0  power_manager.o ABSOLUTE
    ../Core/Src/spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    ../Core/Src/stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_spi.c 0x00000000   Number         0  stm32f1xx_hal_spi.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F1xx_HAL_Driver/Src/stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ../clib/../cmprslib/zerorunl2.c          0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  ctype_c.o ABSOLUTE
    ../clib/microlib/ctype/ctype.c           0x00000000   Number         0  isspace_c.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/string/strstr.c         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_str.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_int.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\power_manager.c              0x00000000   Number         0  power_manager.o ABSOLUTE
    ..\Core\Src\spi.c                        0x00000000   Number         0  spi.o ABSOLUTE
    ..\Core\Src\stm32f1xx_hal_msp.c          0x00000000   Number         0  stm32f1xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f1xx_it.c               0x00000000   Number         0  stm32f1xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f1xx.c           0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal.c 0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_cortex.c 0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_dma.c 0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_exti.c 0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash.c 0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_flash_ex.c 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio.c 0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_gpio_ex.c 0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_pwr.c 0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc.c 0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_spi.c 0x00000000   Number         0  stm32f1xx_hal_spi.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim.c 0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_tim_ex.c 0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F1xx_HAL_Driver\Src\stm32f1xx_hal_uart.c 0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    ..\Hardware\ESP32\esp32.c                0x00000000   Number         0  esp32.o ABSOLUTE
    ..\Hardware\Key\key.c                    0x00000000   Number         0  key.o ABSOLUTE
    ..\Hardware\LCD\bsp_LCD_ILI9341.c        0x00000000   Number         0  bsp_lcd_ili9341.o ABSOLUTE
    ..\Hardware\Led\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\Hardware\RC522\rc522.c                0x00000000   Number         0  rc522.o ABSOLUTE
    ..\Hardware\WIFI\wifi.c                  0x00000000   Number         0  wifi.o ABSOLUTE
    ..\\Hardware\\ESP32\\esp32.c             0x00000000   Number         0  esp32.o ABSOLUTE
    ..\\Hardware\\Key\\key.c                 0x00000000   Number         0  key.o ABSOLUTE
    ..\\Hardware\\LCD\\bsp_LCD_ILI9341.c     0x00000000   Number         0  bsp_lcd_ili9341.o ABSOLUTE
    ..\\Hardware\\Led\\led.c                 0x00000000   Number         0  led.o ABSOLUTE
    ..\\Hardware\\RC522\\rc522.c             0x00000000   Number         0  rc522.o ABSOLUTE
    ..\\Hardware\\WIFI\\wifi.c               0x00000000   Number         0  wifi.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32f103xe.s                    0x00000000   Number         0  startup_stm32f103xe.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f103xe.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000130   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000130   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000134   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000138   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000138   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000138   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x08000140   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x08000140   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x08000140   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x08000140   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x08000144   Section       36  startup_stm32f103xe.o(.text)
    .text                                    0x08000168   Section        0  llushr.o(.text)
    .text                                    0x08000188   Section        0  memseta.o(.text)
    .text                                    0x080001ac   Section        0  strstr.o(.text)
    .text                                    0x080001d0   Section        0  strlen.o(.text)
    .text                                    0x080001de   Section        0  uldiv.o(.text)
    .text                                    0x08000240   Section       36  init.o(.text)
    .text                                    0x08000264   Section        0  llshl.o(.text)
    .text                                    0x08000282   Section        0  __dczerorl2.o(.text)
    i.BusFault_Handler                       0x080002d8   Section        0  stm32f1xx_it.o(i.BusFault_Handler)
    i.DebugMon_Handler                       0x080002da   Section        0  stm32f1xx_it.o(i.DebugMon_Handler)
    i.ESP32_Init                             0x080002dc   Section        0  esp32.o(i.ESP32_Init)
    i.ESP32_SendCmd                          0x08000310   Section        0  esp32.o(i.ESP32_SendCmd)
    i.EXTI0_IRQHandler                       0x08000354   Section        0  stm32f1xx_it.o(i.EXTI0_IRQHandler)
    i.EXTI1_IRQHandler                       0x0800035a   Section        0  stm32f1xx_it.o(i.EXTI1_IRQHandler)
    i.EXTI2_IRQHandler                       0x08000360   Section        0  stm32f1xx_it.o(i.EXTI2_IRQHandler)
    i.EXTI4_IRQHandler                       0x08000366   Section        0  stm32f1xx_it.o(i.EXTI4_IRQHandler)
    i.Error_Handler                          0x0800036c   Section        0  main.o(i.Error_Handler)
    i.HAL_DMA_Abort                          0x08000370   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x080003b8   Section        0  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_Delay                              0x080004e8   Section        0  stm32f1xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_EXTI_Callback                 0x0800050c   Section        0  key.o(i.HAL_GPIO_EXTI_Callback)
    i.HAL_GPIO_EXTI_IRQHandler               0x08000534   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    i.HAL_GPIO_Init                          0x0800054c   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x08000744   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x0800074e   Section        0  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08000758   Section        0  stm32f1xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08000764   Section        0  stm32f1xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08000774   Section        0  stm32f1xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08000798   Section        0  stm32f1xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x080007d8   Section        0  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08000814   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08000830   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08000870   Section        0  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_PWR_EnterSTOPMode                  0x08000894   Section        0  stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode)
    i.HAL_RCC_ClockConfig                    0x080008d8   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x08000a04   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08000a24   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08000a44   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08000a90   Section        0  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SPI_Init                           0x08000db0   Section        0  stm32f1xx_hal_spi.o(i.HAL_SPI_Init)
    i.HAL_SPI_MspInit                        0x08000e64   Section        0  spi.o(i.HAL_SPI_MspInit)
    i.HAL_SPI_TransmitReceive                0x08000ed4   Section        0  stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive)
    i.HAL_SYSTICK_Config                     0x080010c4   Section        0  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_UARTEx_ReceiveToIdle_IT            0x080010ec   Section        0  stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT)
    i.HAL_UARTEx_RxEventCallback             0x0800113c   Section        0  usart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_ErrorCallback                 0x08001168   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x0800116c   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x080013d8   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x0800143c   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive                       0x0800150c   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Receive)
    i.HAL_UART_Receive_IT                    0x080015bc   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x080015d8   Section        0  usart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_Transmit                      0x08001604   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x080016a4   Section        0  stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x080016a6   Section        0  stm32f1xx_it.o(i.HardFault_Handler)
    i.Key2_Status                            0x080016a8   Section        0  key.o(i.Key2_Status)
    i.LCD_DrawPoint                          0x080016d0   Section        0  bsp_lcd_ili9341.o(i.LCD_DrawPoint)
    i.LCD_Fill                               0x0800173c   Section        0  bsp_lcd_ili9341.o(i.LCD_Fill)
    i.LCD_Init                               0x08001778   Section        0  bsp_lcd_ili9341.o(i.LCD_Init)
    i.LCD_SetDir                             0x08001ab0   Section        0  bsp_lcd_ili9341.o(i.LCD_SetDir)
    i.LCD_ShowChinese                        0x08001b14   Section        0  bsp_lcd_ili9341.o(i.LCD_ShowChinese)
    i.LCD_String                             0x08001bf0   Section        0  bsp_lcd_ili9341.o(i.LCD_String)
    i.MFRC522_AntennaOn                      0x08001c88   Section        0  rc522.o(i.MFRC522_AntennaOn)
    i.MFRC522_Anticoll                       0x08001ca2   Section        0  rc522.o(i.MFRC522_Anticoll)
    i.MFRC522_CalculateCRC                   0x08001ce4   Section        0  rc522.o(i.MFRC522_CalculateCRC)
    i.MFRC522_Check                          0x08001d42   Section        0  rc522.o(i.MFRC522_Check)
    i.MFRC522_ClearBitMask                   0x08001d62   Section        0  rc522.o(i.MFRC522_ClearBitMask)
    i.MFRC522_Compare                        0x08001d7a   Section        0  rc522.o(i.MFRC522_Compare)
    i.MFRC522_Halt                           0x08001d96   Section        0  rc522.o(i.MFRC522_Halt)
    i.MFRC522_Init                           0x08001dc2   Section        0  rc522.o(i.MFRC522_Init)
    i.MFRC522_ReadRegister                   0x08001e20   Section        0  rc522.o(i.MFRC522_ReadRegister)
    i.MFRC522_Request                        0x08001e2e   Section        0  rc522.o(i.MFRC522_Request)
    i.MFRC522_Reset                          0x08001e5c   Section        0  rc522.o(i.MFRC522_Reset)
    i.MFRC522_SelectTag                      0x08001e64   Section        0  rc522.o(i.MFRC522_SelectTag)
    i.MFRC522_SetBitMask                     0x08001eb6   Section        0  rc522.o(i.MFRC522_SetBitMask)
    i.MFRC522_ToCard                         0x08001ece   Section        0  rc522.o(i.MFRC522_ToCard)
    i.MFRC522_WriteRegister                  0x08001fe2   Section        0  rc522.o(i.MFRC522_WriteRegister)
    i.MX_GPIO_Init                           0x08001fec   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_SPI1_Init                           0x0800212c   Section        0  spi.o(i.MX_SPI1_Init)
    i.MX_UART4_Init                          0x08002174   Section        0  usart.o(i.MX_UART4_Init)
    i.MX_USART1_UART_Init                    0x080021ac   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MemManage_Handler                      0x080021e4   Section        0  stm32f1xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080021e6   Section        0  stm32f1xx_it.o(i.NMI_Handler)
    i.PWR_OverloadWfe                        0x080021e8   Section        0  stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe)
    PWR_OverloadWfe                          0x080021e9   Thumb Code     6  stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe)
    __tagsym$$noinline                       0x080021e9   Number         0  stm32f1xx_hal_pwr.o(i.PWR_OverloadWfe)
    i.PendSV_Handler                         0x080021ee   Section        0  stm32f1xx_it.o(i.PendSV_Handler)
    i.PowerManager_CheckTimeout              0x080021f0   Section        0  power_manager.o(i.PowerManager_CheckTimeout)
    i.PowerManager_EnterStopMode             0x08002248   Section        0  power_manager.o(i.PowerManager_EnterStopMode)
    i.PowerManager_GetState                  0x08002278   Section        0  power_manager.o(i.PowerManager_GetState)
    i.PowerManager_HandleRC522Interrupt      0x08002284   Section        0  power_manager.o(i.PowerManager_HandleRC522Interrupt)
    i.PowerManager_Init                      0x080022a0   Section        0  power_manager.o(i.PowerManager_Init)
    i.PowerManager_PrepareForSleep           0x08002308   Section        0  power_manager.o(i.PowerManager_PrepareForSleep)
    PowerManager_PrepareForSleep             0x08002309   Thumb Code   130  power_manager.o(i.PowerManager_PrepareForSleep)
    i.PowerManager_RestoreWiFiConnection     0x08002400   Section        0  power_manager.o(i.PowerManager_RestoreWiFiConnection)
    i.PowerManager_UpdateCardDetectTime      0x08002574   Section        0  power_manager.o(i.PowerManager_UpdateCardDetectTime)
    i.PowerManager_WakeupRestore             0x08002584   Section        0  power_manager.o(i.PowerManager_WakeupRestore)
    i.SPI1SendByte                           0x080026a0   Section        0  rc522.o(i.SPI1SendByte)
    i.SPI1_ReadReg                           0x080026c0   Section        0  rc522.o(i.SPI1_ReadReg)
    i.SPI1_WriteReg                          0x080026f0   Section        0  rc522.o(i.SPI1_WriteReg)
    i.SPI_EndRxTxTransaction                 0x08002720   Section        0  stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction)
    SPI_EndRxTxTransaction                   0x08002721   Thumb Code    52  stm32f1xx_hal_spi.o(i.SPI_EndRxTxTransaction)
    i.SPI_WaitFlagStateUntilTimeout          0x08002754   Section        0  stm32f1xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout)
    SPI_WaitFlagStateUntilTimeout            0x08002755   Thumb Code   180  stm32f1xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout)
    i.SVC_Handler                            0x0800280c   Section        0  stm32f1xx_it.o(i.SVC_Handler)
    i.String_Index                           0x08002810   Section        0  bsp_lcd_ili9341.o(i.String_Index)
    i.SysTick_Handler                        0x08002858   Section        0  stm32f1xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x0800285c   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x080028ba   Section        0  system_stm32f1xx.o(i.SystemInit)
    i.UART_DMAAbortOnError                   0x080028bc   Section        0  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x080028bd   Thumb Code    16  stm32f1xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_EndRxTransfer                     0x080028cc   Section        0  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x080028cd   Thumb Code    78  stm32f1xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_Receive_IT                        0x0800291a   Section        0  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x0800291b   Thumb Code   194  stm32f1xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x080029dc   Section        0  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x080029dd   Thumb Code   178  stm32f1xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_IT                  0x08002a94   Section        0  stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_WaitOnFlagUntilTimeout            0x08002aca   Section        0  stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x08002acb   Thumb Code   114  stm32f1xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08002b3c   Section        0  stm32f1xx_it.o(i.USART1_IRQHandler)
    i.UsageFault_Handler                     0x08002b48   Section        0  stm32f1xx_it.o(i.UsageFault_Handler)
    i.WIFI_TCP_SendData                      0x08002b4c   Section        0  wifi.o(i.WIFI_TCP_SendData)
    i.__0printf$8                            0x08002ba4   Section        0  printf8.o(i.__0printf$8)
    i.__0sprintf$8                           0x08002bc4   Section        0  printf8.o(i.__0sprintf$8)
    i.__NVIC_SetPriority                     0x08002bec   Section        0  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08002bed   Thumb Code    32  stm32f1xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__scatterload_copy                     0x08002c0c   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08002c1a   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08002c1c   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._printf_core                           0x08002c2c   Section        0  printf8.o(i._printf_core)
    _printf_core                             0x08002c2d   Thumb Code   996  printf8.o(i._printf_core)
    i._printf_post_padding                   0x0800303c   Section        0  printf8.o(i._printf_post_padding)
    _printf_post_padding                     0x0800303d   Thumb Code    36  printf8.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08003060   Section        0  printf8.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08003061   Thumb Code    46  printf8.o(i._printf_pre_padding)
    i._sputc                                 0x0800308e   Section        0  printf8.o(i._sputc)
    _sputc                                   0x0800308f   Thumb Code    10  printf8.o(i._sputc)
    i.connectTcpServer                       0x08003098   Section        0  esp32.o(i.connectTcpServer)
    i.connectWiFi                            0x080030e8   Section        0  esp32.o(i.connectWiFi)
    i.delay_ms                               0x08003130   Section        0  bsp_lcd_ili9341.o(i.delay_ms)
    delay_ms                                 0x08003131   Thumb Code    24  bsp_lcd_ili9341.o(i.delay_ms)
    i.drawAscii                              0x0800314c   Section        0  bsp_lcd_ili9341.o(i.drawAscii)
    i.fputc                                  0x08003230   Section        0  usart.o(i.fputc)
    i.main                                   0x08003248   Section        0  main.o(i.main)
    i.readData                               0x0800393c   Section        0  bsp_lcd_ili9341.o(i.readData)
    i.sendData                               0x08003984   Section        0  esp32.o(i.sendData)
    i.sendDataShort                          0x080039f8   Section        0  bsp_lcd_ili9341.o(i.sendDataShort)
    i.sendOrder                              0x08003a1c   Section        0  bsp_lcd_ili9341.o(i.sendOrder)
    i.setATMode                              0x08003a40   Section        0  esp32.o(i.setATMode)
    i.setCursor                              0x08003a7c   Section        0  bsp_lcd_ili9341.o(i.setCursor)
    .constdata                               0x08003b04   Section       18  stm32f1xx_hal_rcc.o(.constdata)
    aPredivFactorTable                       0x08003b04   Data           2  stm32f1xx_hal_rcc.o(.constdata)
    aPLLMULFactorTable                       0x08003b06   Data          16  stm32f1xx_hal_rcc.o(.constdata)
    .constdata                               0x08003b16   Section       16  system_stm32f1xx.o(.constdata)
    .constdata                               0x08003b26   Section        8  system_stm32f1xx.o(.constdata)
    .constdata                               0x08003b2e   Section    13128  bsp_lcd_ili9341.o(.constdata)
    .data                                    0x20000000   Section     1037  main.o(.data)
    card_processed                           0x20000000   Data           1  main.o(.data)
    last_clear_time                          0x20000004   Data           4  main.o(.data)
    last_card_id                             0x20000008   Data           5  main.o(.data)
    .data                                    0x2000040e   Section        4  usart.o(.data)
    .data                                    0x20000414   Section       12  power_manager.o(.data)
    system_state                             0x20000414   Data           1  power_manager.o(.data)
    wakeup_flag                              0x20000415   Data           1  power_manager.o(.data)
    last_card_detect_time                    0x20000418   Data           4  power_manager.o(.data)
    sleep_timeout                            0x2000041c   Data           4  power_manager.o(.data)
    .data                                    0x20000420   Section       12  stm32f1xx_hal.o(.data)
    .data                                    0x2000042c   Section        4  system_stm32f1xx.o(.data)
    .data                                    0x20000430   Section        3  key.o(.data)
    .data                                    0x20000434   Section      144  bsp_lcd_ili9341.o(.data)
    temp                                     0x20000434   Data           1  bsp_lcd_ili9341.o(.data)
    csize                                    0x20000435   Data           1  bsp_lcd_ili9341.o(.data)
    y0                                       0x20000436   Data           2  bsp_lcd_ili9341.o(.data)
    ulTimesMS                                0x20000438   Data           4  bsp_lcd_ili9341.o(.data)
    .data                                    0x200004c4   Section        4  stdout.o(.data)
    .bss                                     0x200004c8   Section       88  spi.o(.bss)
    .bss                                     0x20000520   Section     1168  usart.o(.bss)
    .bss                                     0x200009b0   Section       10  bsp_lcd_ili9341.o(.bss)
    STACK                                    0x200009c0   Section     1024  startup_stm32f103xe.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_real                               - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f103xe.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f103xe.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f103xe.o(RESET)
    __main                                   0x08000131   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000131   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000135   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000139   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000139   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000139   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000139   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x08000141   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x08000141   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x08000145   Thumb Code     8  startup_stm32f103xe.o(.text)
    ADC1_2_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    ADC3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    CAN1_RX1_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    CAN1_SCE_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel1_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel2_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel3_IRQHandler                 0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    DMA2_Channel4_5_IRQHandler               0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI15_10_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI3_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    EXTI9_5_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    FLASH_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    FSMC_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C1_ER_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C1_EV_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C2_ER_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    I2C2_EV_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    PVD_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    RCC_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    RTC_Alarm_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    RTC_IRQHandler                           0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    SDIO_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    SPI1_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    SPI2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    SPI3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TAMPER_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_BRK_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_CC_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM1_UP_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM2_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM3_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM4_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM5_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM6_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM7_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_BRK_IRQHandler                      0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_CC_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    TIM8_UP_IRQHandler                       0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    UART4_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    UART5_IRQHandler                         0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    USART2_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    USART3_IRQHandler                        0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    USBWakeUp_IRQHandler                     0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    WWDG_IRQHandler                          0x0800015f   Thumb Code     0  startup_stm32f103xe.o(.text)
    __aeabi_llsr                             0x08000169   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000169   Thumb Code     0  llushr.o(.text)
    __aeabi_memset                           0x08000189   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000189   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000189   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000197   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000197   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000197   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0800019b   Thumb Code    18  memseta.o(.text)
    strstr                                   0x080001ad   Thumb Code    36  strstr.o(.text)
    strlen                                   0x080001d1   Thumb Code    14  strlen.o(.text)
    __aeabi_uldivmod                         0x080001df   Thumb Code    98  uldiv.o(.text)
    __scatterload                            0x08000241   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000241   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x08000265   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000265   Thumb Code     0  llshl.o(.text)
    __decompress                             0x08000283   Thumb Code     0  __dczerorl2.o(.text)
    __decompress1                            0x08000283   Thumb Code    86  __dczerorl2.o(.text)
    BusFault_Handler                         0x080002d9   Thumb Code     2  stm32f1xx_it.o(i.BusFault_Handler)
    DebugMon_Handler                         0x080002db   Thumb Code     2  stm32f1xx_it.o(i.DebugMon_Handler)
    ESP32_Init                               0x080002dd   Thumb Code    30  esp32.o(i.ESP32_Init)
    ESP32_SendCmd                            0x08000311   Thumb Code    64  esp32.o(i.ESP32_SendCmd)
    EXTI0_IRQHandler                         0x08000355   Thumb Code     6  stm32f1xx_it.o(i.EXTI0_IRQHandler)
    EXTI1_IRQHandler                         0x0800035b   Thumb Code     6  stm32f1xx_it.o(i.EXTI1_IRQHandler)
    EXTI2_IRQHandler                         0x08000361   Thumb Code     6  stm32f1xx_it.o(i.EXTI2_IRQHandler)
    EXTI4_IRQHandler                         0x08000367   Thumb Code     6  stm32f1xx_it.o(i.EXTI4_IRQHandler)
    Error_Handler                            0x0800036d   Thumb Code     4  main.o(i.Error_Handler)
    HAL_DMA_Abort                            0x08000371   Thumb Code    70  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x080003b9   Thumb Code   296  stm32f1xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_Delay                                0x080004e9   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Delay)
    HAL_GPIO_EXTI_Callback                   0x0800050d   Thumb Code    36  key.o(i.HAL_GPIO_EXTI_Callback)
    HAL_GPIO_EXTI_IRQHandler                 0x08000535   Thumb Code    18  stm32f1xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    HAL_GPIO_Init                            0x0800054d   Thumb Code   462  stm32f1xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08000745   Thumb Code    10  stm32f1xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x0800074f   Thumb Code    10  stm32f1xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08000759   Thumb Code     6  stm32f1xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08000765   Thumb Code    12  stm32f1xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08000775   Thumb Code    32  stm32f1xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08000799   Thumb Code    54  stm32f1xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x080007d9   Thumb Code    52  stm32f1xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08000815   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08000831   Thumb Code    60  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08000871   Thumb Code    26  stm32f1xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_PWR_EnterSTOPMode                    0x08000895   Thumb Code    60  stm32f1xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode)
    HAL_RCC_ClockConfig                      0x080008d9   Thumb Code   280  stm32f1xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08000a05   Thumb Code    20  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08000a25   Thumb Code    20  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08000a45   Thumb Code    58  stm32f1xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08000a91   Thumb Code   778  stm32f1xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SPI_Init                             0x08000db1   Thumb Code   178  stm32f1xx_hal_spi.o(i.HAL_SPI_Init)
    HAL_SPI_MspInit                          0x08000e65   Thumb Code    98  spi.o(i.HAL_SPI_MspInit)
    HAL_SPI_TransmitReceive                  0x08000ed5   Thumb Code   496  stm32f1xx_hal_spi.o(i.HAL_SPI_TransmitReceive)
    HAL_SYSTICK_Config                       0x080010c5   Thumb Code    40  stm32f1xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_UARTEx_ReceiveToIdle_IT              0x080010ed   Thumb Code    78  stm32f1xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT)
    HAL_UARTEx_RxEventCallback               0x0800113d   Thumb Code    32  usart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x08001169   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x0800116d   Thumb Code   616  stm32f1xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x080013d9   Thumb Code   100  stm32f1xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x0800143d   Thumb Code   186  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive                         0x0800150d   Thumb Code   176  stm32f1xx_hal_uart.o(i.HAL_UART_Receive)
    HAL_UART_Receive_IT                      0x080015bd   Thumb Code    28  stm32f1xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x080015d9   Thumb Code    30  usart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_Transmit                        0x08001605   Thumb Code   160  stm32f1xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x080016a5   Thumb Code     2  stm32f1xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x080016a7   Thumb Code     2  stm32f1xx_it.o(i.HardFault_Handler)
    Key2_Status                              0x080016a9   Thumb Code    34  key.o(i.Key2_Status)
    LCD_DrawPoint                            0x080016d1   Thumb Code    98  bsp_lcd_ili9341.o(i.LCD_DrawPoint)
    LCD_Fill                                 0x0800173d   Thumb Code    50  bsp_lcd_ili9341.o(i.LCD_Fill)
    LCD_Init                                 0x08001779   Thumb Code   802  bsp_lcd_ili9341.o(i.LCD_Init)
    LCD_SetDir                               0x08001ab1   Thumb Code    96  bsp_lcd_ili9341.o(i.LCD_SetDir)
    LCD_ShowChinese                          0x08001b15   Thumb Code   206  bsp_lcd_ili9341.o(i.LCD_ShowChinese)
    LCD_String                               0x08001bf1   Thumb Code   148  bsp_lcd_ili9341.o(i.LCD_String)
    MFRC522_AntennaOn                        0x08001c89   Thumb Code    26  rc522.o(i.MFRC522_AntennaOn)
    MFRC522_Anticoll                         0x08001ca3   Thumb Code    66  rc522.o(i.MFRC522_Anticoll)
    MFRC522_CalculateCRC                     0x08001ce5   Thumb Code    94  rc522.o(i.MFRC522_CalculateCRC)
    MFRC522_Check                            0x08001d43   Thumb Code    32  rc522.o(i.MFRC522_Check)
    MFRC522_ClearBitMask                     0x08001d63   Thumb Code    24  rc522.o(i.MFRC522_ClearBitMask)
    MFRC522_Compare                          0x08001d7b   Thumb Code    28  rc522.o(i.MFRC522_Compare)
    MFRC522_Halt                             0x08001d97   Thumb Code    44  rc522.o(i.MFRC522_Halt)
    MFRC522_Init                             0x08001dc3   Thumb Code    94  rc522.o(i.MFRC522_Init)
    MFRC522_ReadRegister                     0x08001e21   Thumb Code    14  rc522.o(i.MFRC522_ReadRegister)
    MFRC522_Request                          0x08001e2f   Thumb Code    46  rc522.o(i.MFRC522_Request)
    MFRC522_Reset                            0x08001e5d   Thumb Code     8  rc522.o(i.MFRC522_Reset)
    MFRC522_SelectTag                        0x08001e65   Thumb Code    82  rc522.o(i.MFRC522_SelectTag)
    MFRC522_SetBitMask                       0x08001eb7   Thumb Code    24  rc522.o(i.MFRC522_SetBitMask)
    MFRC522_ToCard                           0x08001ecf   Thumb Code   276  rc522.o(i.MFRC522_ToCard)
    MFRC522_WriteRegister                    0x08001fe3   Thumb Code    10  rc522.o(i.MFRC522_WriteRegister)
    MX_GPIO_Init                             0x08001fed   Thumb Code   296  gpio.o(i.MX_GPIO_Init)
    MX_SPI1_Init                             0x0800212d   Thumb Code    62  spi.o(i.MX_SPI1_Init)
    MX_UART4_Init                            0x08002175   Thumb Code    48  usart.o(i.MX_UART4_Init)
    MX_USART1_UART_Init                      0x080021ad   Thumb Code    48  usart.o(i.MX_USART1_UART_Init)
    MemManage_Handler                        0x080021e5   Thumb Code     2  stm32f1xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080021e7   Thumb Code     2  stm32f1xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x080021ef   Thumb Code     2  stm32f1xx_it.o(i.PendSV_Handler)
    PowerManager_CheckTimeout                0x080021f1   Thumb Code    40  power_manager.o(i.PowerManager_CheckTimeout)
    PowerManager_EnterStopMode               0x08002249   Thumb Code    42  power_manager.o(i.PowerManager_EnterStopMode)
    PowerManager_GetState                    0x08002279   Thumb Code     6  power_manager.o(i.PowerManager_GetState)
    PowerManager_HandleRC522Interrupt        0x08002285   Thumb Code    22  power_manager.o(i.PowerManager_HandleRC522Interrupt)
    PowerManager_Init                        0x080022a1   Thumb Code    62  power_manager.o(i.PowerManager_Init)
    PowerManager_RestoreWiFiConnection       0x08002401   Thumb Code   174  power_manager.o(i.PowerManager_RestoreWiFiConnection)
    PowerManager_UpdateCardDetectTime        0x08002575   Thumb Code    12  power_manager.o(i.PowerManager_UpdateCardDetectTime)
    PowerManager_WakeupRestore               0x08002585   Thumb Code   160  power_manager.o(i.PowerManager_WakeupRestore)
    SPI1SendByte                             0x080026a1   Thumb Code    28  rc522.o(i.SPI1SendByte)
    SPI1_ReadReg                             0x080026c1   Thumb Code    44  rc522.o(i.SPI1_ReadReg)
    SPI1_WriteReg                            0x080026f1   Thumb Code    44  rc522.o(i.SPI1_WriteReg)
    SVC_Handler                              0x0800280d   Thumb Code     2  stm32f1xx_it.o(i.SVC_Handler)
    String_Index                             0x08002811   Thumb Code    66  bsp_lcd_ili9341.o(i.String_Index)
    SysTick_Handler                          0x08002859   Thumb Code     4  stm32f1xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x0800285d   Thumb Code    94  main.o(i.SystemClock_Config)
    SystemInit                               0x080028bb   Thumb Code     2  system_stm32f1xx.o(i.SystemInit)
    UART_Start_Receive_IT                    0x08002a95   Thumb Code    54  stm32f1xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART1_IRQHandler                        0x08002b3d   Thumb Code     6  stm32f1xx_it.o(i.USART1_IRQHandler)
    UsageFault_Handler                       0x08002b49   Thumb Code     2  stm32f1xx_it.o(i.UsageFault_Handler)
    WIFI_TCP_SendData                        0x08002b4d   Thumb Code    60  wifi.o(i.WIFI_TCP_SendData)
    __0printf$8                              0x08002ba5   Thumb Code    22  printf8.o(i.__0printf$8)
    __1printf$8                              0x08002ba5   Thumb Code     0  printf8.o(i.__0printf$8)
    __2printf                                0x08002ba5   Thumb Code     0  printf8.o(i.__0printf$8)
    __0sprintf$8                             0x08002bc5   Thumb Code    34  printf8.o(i.__0sprintf$8)
    __1sprintf$8                             0x08002bc5   Thumb Code     0  printf8.o(i.__0sprintf$8)
    __2sprintf                               0x08002bc5   Thumb Code     0  printf8.o(i.__0sprintf$8)
    __scatterload_copy                       0x08002c0d   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08002c1b   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08002c1d   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    connectTcpServer                         0x08003099   Thumb Code    44  esp32.o(i.connectTcpServer)
    connectWiFi                              0x080030e9   Thumb Code    44  esp32.o(i.connectWiFi)
    drawAscii                                0x0800314d   Thumb Code   202  bsp_lcd_ili9341.o(i.drawAscii)
    fputc                                    0x08003231   Thumb Code    20  usart.o(i.fputc)
    main                                     0x08003249   Thumb Code  1014  main.o(i.main)
    readData                                 0x0800393d   Thumb Code    64  bsp_lcd_ili9341.o(i.readData)
    sendData                                 0x08003985   Thumb Code    80  esp32.o(i.sendData)
    sendDataShort                            0x080039f9   Thumb Code    26  bsp_lcd_ili9341.o(i.sendDataShort)
    sendOrder                                0x08003a1d   Thumb Code    26  bsp_lcd_ili9341.o(i.sendOrder)
    setATMode                                0x08003a41   Thumb Code    40  esp32.o(i.setATMode)
    setCursor                                0x08003a7d   Thumb Code   126  bsp_lcd_ili9341.o(i.setCursor)
    AHBPrescTable                            0x08003b16   Data          16  system_stm32f1xx.o(.constdata)
    APBPrescTable                            0x08003b26   Data           8  system_stm32f1xx.o(.constdata)
    aFontChinese12                           0x08003b2e   Data          96  bsp_lcd_ili9341.o(.constdata)
    aFontChinese24                           0x08003b8e   Data         360  bsp_lcd_ili9341.o(.constdata)
    aFontChinese32                           0x08003cf6   Data         512  bsp_lcd_ili9341.o(.constdata)
    aFontASCII12                             0x08003ef6   Data        1140  bsp_lcd_ili9341.o(.constdata)
    aFontASCII16                             0x0800436a   Data        1520  bsp_lcd_ili9341.o(.constdata)
    aFontASCII24                             0x0800495a   Data        3420  bsp_lcd_ili9341.o(.constdata)
    aFontASCII32                             0x080056b6   Data        6080  bsp_lcd_ili9341.o(.constdata)
    Region$$Table$$Base                      0x08006e78   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08006e98   Number         0  anon$$obj.o(Region$$Table)
    rxBuff                                   0x2000000d   Data        1024  main.o(.data)
    is_RecvOk                                0x2000040e   Data           1  usart.o(.data)
    len                                      0x20000410   Data           2  usart.o(.data)
    uwTickFreq                               0x20000420   Data           1  stm32f1xx_hal.o(.data)
    uwTickPrio                               0x20000424   Data           4  stm32f1xx_hal.o(.data)
    uwTick                                   0x20000428   Data           4  stm32f1xx_hal.o(.data)
    SystemCoreClock                          0x2000042c   Data           4  system_stm32f1xx.o(.data)
    key1_flag                                0x20000430   Data           1  key.o(.data)
    key2_flag                                0x20000431   Data           1  key.o(.data)
    key3_flag                                0x20000432   Data           1  key.o(.data)
    z_GB_16                                  0x2000043c   Data         136  bsp_lcd_ili9341.o(.data)
    __stdout                                 0x200004c4   Data           4  stdout.o(.data)
    hspi1                                    0x200004c8   Data          88  spi.o(.bss)
    huart4                                   0x20000520   Data          72  usart.o(.bss)
    huart1                                   0x20000568   Data          72  usart.o(.bss)
    buff                                     0x200005b0   Data        1024  usart.o(.bss)
    xLCD                                     0x200009b0   Data          10  bsp_lcd_ili9341.o(.bss)
    __initial_sp                             0x20000dc0   Data           0  startup_stm32f103xe.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00007360, Max: 0x00040000, ABSOLUTE, COMPRESSED[0x00006f30])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00006e98, Max: 0x00040000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO            3    RESET               startup_stm32f103xe.o
    0x08000130   0x08000130   0x00000000   Code   RO         2881  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000130   0x08000130   0x00000004   Code   RO         3163    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000134   0x08000134   0x00000004   Code   RO         3166    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000138   0x08000138   0x00000000   Code   RO         3168    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000138   0x08000138   0x00000000   Code   RO         3170    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000138   0x08000138   0x00000008   Code   RO         3171    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000140   0x08000140   0x00000000   Code   RO         3173    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x08000140   0x08000140   0x00000000   Code   RO         3175    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x08000140   0x08000140   0x00000004   Code   RO         3164    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x08000144   0x08000144   0x00000024   Code   RO            4    .text               startup_stm32f103xe.o
    0x08000168   0x08000168   0x00000020   Code   RO         2884    .text               mc_w.l(llushr.o)
    0x08000188   0x08000188   0x00000024   Code   RO         2888    .text               mc_w.l(memseta.o)
    0x080001ac   0x080001ac   0x00000024   Code   RO         2890    .text               mc_w.l(strstr.o)
    0x080001d0   0x080001d0   0x0000000e   Code   RO         2892    .text               mc_w.l(strlen.o)
    0x080001de   0x080001de   0x00000062   Code   RO         3180    .text               mc_w.l(uldiv.o)
    0x08000240   0x08000240   0x00000024   Code   RO         3199    .text               mc_w.l(init.o)
    0x08000264   0x08000264   0x0000001e   Code   RO         3201    .text               mc_w.l(llshl.o)
    0x08000282   0x08000282   0x00000056   Code   RO         3222    .text               mc_w.l(__dczerorl2.o)
    0x080002d8   0x080002d8   0x00000002   Code   RO          314    i.BusFault_Handler  stm32f1xx_it.o
    0x080002da   0x080002da   0x00000002   Code   RO          315    i.DebugMon_Handler  stm32f1xx_it.o
    0x080002dc   0x080002dc   0x00000034   Code   RO         2602    i.ESP32_Init        esp32.o
    0x08000310   0x08000310   0x00000044   Code   RO         2604    i.ESP32_SendCmd     esp32.o
    0x08000354   0x08000354   0x00000006   Code   RO          316    i.EXTI0_IRQHandler  stm32f1xx_it.o
    0x0800035a   0x0800035a   0x00000006   Code   RO          317    i.EXTI1_IRQHandler  stm32f1xx_it.o
    0x08000360   0x08000360   0x00000006   Code   RO          318    i.EXTI2_IRQHandler  stm32f1xx_it.o
    0x08000366   0x08000366   0x00000006   Code   RO          319    i.EXTI4_IRQHandler  stm32f1xx_it.o
    0x0800036c   0x0800036c   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x08000370   0x08000370   0x00000046   Code   RO         1262    i.HAL_DMA_Abort     stm32f1xx_hal_dma.o
    0x080003b6   0x080003b6   0x00000002   PAD
    0x080003b8   0x080003b8   0x00000130   Code   RO         1263    i.HAL_DMA_Abort_IT  stm32f1xx_hal_dma.o
    0x080004e8   0x080004e8   0x00000024   Code   RO          888    i.HAL_Delay         stm32f1xx_hal.o
    0x0800050c   0x0800050c   0x00000028   Code   RO         2350    i.HAL_GPIO_EXTI_Callback  key.o
    0x08000534   0x08000534   0x00000018   Code   RO         1197    i.HAL_GPIO_EXTI_IRQHandler  stm32f1xx_hal_gpio.o
    0x0800054c   0x0800054c   0x000001f8   Code   RO         1198    i.HAL_GPIO_Init     stm32f1xx_hal_gpio.o
    0x08000744   0x08000744   0x0000000a   Code   RO         1200    i.HAL_GPIO_ReadPin  stm32f1xx_hal_gpio.o
    0x0800074e   0x0800074e   0x0000000a   Code   RO         1202    i.HAL_GPIO_WritePin  stm32f1xx_hal_gpio.o
    0x08000758   0x08000758   0x0000000c   Code   RO          892    i.HAL_GetTick       stm32f1xx_hal.o
    0x08000764   0x08000764   0x00000010   Code   RO          898    i.HAL_IncTick       stm32f1xx_hal.o
    0x08000774   0x08000774   0x00000024   Code   RO          899    i.HAL_Init          stm32f1xx_hal.o
    0x08000798   0x08000798   0x00000040   Code   RO          900    i.HAL_InitTick      stm32f1xx_hal.o
    0x080007d8   0x080007d8   0x0000003c   Code   RO          420    i.HAL_MspInit       stm32f1xx_hal_msp.o
    0x08000814   0x08000814   0x0000001a   Code   RO         1358    i.HAL_NVIC_EnableIRQ  stm32f1xx_hal_cortex.o
    0x0800082e   0x0800082e   0x00000002   PAD
    0x08000830   0x08000830   0x00000040   Code   RO         1364    i.HAL_NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08000870   0x08000870   0x00000024   Code   RO         1365    i.HAL_NVIC_SetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x08000894   0x08000894   0x00000044   Code   RO         1487    i.HAL_PWR_EnterSTOPMode  stm32f1xx_hal_pwr.o
    0x080008d8   0x080008d8   0x0000012c   Code   RO         1056    i.HAL_RCC_ClockConfig  stm32f1xx_hal_rcc.o
    0x08000a04   0x08000a04   0x00000020   Code   RO         1063    i.HAL_RCC_GetPCLK1Freq  stm32f1xx_hal_rcc.o
    0x08000a24   0x08000a24   0x00000020   Code   RO         1064    i.HAL_RCC_GetPCLK2Freq  stm32f1xx_hal_rcc.o
    0x08000a44   0x08000a44   0x0000004c   Code   RO         1065    i.HAL_RCC_GetSysClockFreq  stm32f1xx_hal_rcc.o
    0x08000a90   0x08000a90   0x00000320   Code   RO         1068    i.HAL_RCC_OscConfig  stm32f1xx_hal_rcc.o
    0x08000db0   0x08000db0   0x000000b2   Code   RO          570    i.HAL_SPI_Init      stm32f1xx_hal_spi.o
    0x08000e62   0x08000e62   0x00000002   PAD
    0x08000e64   0x08000e64   0x00000070   Code   RO          206    i.HAL_SPI_MspInit   spi.o
    0x08000ed4   0x08000ed4   0x000001f0   Code   RO          579    i.HAL_SPI_TransmitReceive  stm32f1xx_hal_spi.o
    0x080010c4   0x080010c4   0x00000028   Code   RO         1369    i.HAL_SYSTICK_Config  stm32f1xx_hal_cortex.o
    0x080010ec   0x080010ec   0x0000004e   Code   RO         1905    i.HAL_UARTEx_ReceiveToIdle_IT  stm32f1xx_hal_uart.o
    0x0800113a   0x0800113a   0x00000002   PAD
    0x0800113c   0x0800113c   0x0000002c   Code   RO          247    i.HAL_UARTEx_RxEventCallback  usart.o
    0x08001168   0x08001168   0x00000002   Code   RO         1920    i.HAL_UART_ErrorCallback  stm32f1xx_hal_uart.o
    0x0800116a   0x0800116a   0x00000002   PAD
    0x0800116c   0x0800116c   0x0000026c   Code   RO         1923    i.HAL_UART_IRQHandler  stm32f1xx_hal_uart.o
    0x080013d8   0x080013d8   0x00000064   Code   RO         1924    i.HAL_UART_Init     stm32f1xx_hal_uart.o
    0x0800143c   0x0800143c   0x000000d0   Code   RO          249    i.HAL_UART_MspInit  usart.o
    0x0800150c   0x0800150c   0x000000b0   Code   RO         1927    i.HAL_UART_Receive  stm32f1xx_hal_uart.o
    0x080015bc   0x080015bc   0x0000001c   Code   RO         1929    i.HAL_UART_Receive_IT  stm32f1xx_hal_uart.o
    0x080015d8   0x080015d8   0x0000002c   Code   RO          250    i.HAL_UART_RxCpltCallback  usart.o
    0x08001604   0x08001604   0x000000a0   Code   RO         1932    i.HAL_UART_Transmit  stm32f1xx_hal_uart.o
    0x080016a4   0x080016a4   0x00000002   Code   RO         1935    i.HAL_UART_TxCpltCallback  stm32f1xx_hal_uart.o
    0x080016a6   0x080016a6   0x00000002   Code   RO          320    i.HardFault_Handler  stm32f1xx_it.o
    0x080016a8   0x080016a8   0x00000028   Code   RO         2352    i.Key2_Status       key.o
    0x080016d0   0x080016d0   0x0000006c   Code   RO         2404    i.LCD_DrawPoint     bsp_lcd_ili9341.o
    0x0800173c   0x0800173c   0x0000003c   Code   RO         2405    i.LCD_Fill          bsp_lcd_ili9341.o
    0x08001778   0x08001778   0x00000338   Code   RO         2411    i.LCD_Init          bsp_lcd_ili9341.o
    0x08001ab0   0x08001ab0   0x00000064   Code   RO         2415    i.LCD_SetDir        bsp_lcd_ili9341.o
    0x08001b14   0x08001b14   0x000000dc   Code   RO         2416    i.LCD_ShowChinese   bsp_lcd_ili9341.o
    0x08001bf0   0x08001bf0   0x00000098   Code   RO         2417    i.LCD_String        bsp_lcd_ili9341.o
    0x08001c88   0x08001c88   0x0000001a   Code   RO         2729    i.MFRC522_AntennaOn  rc522.o
    0x08001ca2   0x08001ca2   0x00000042   Code   RO         2730    i.MFRC522_Anticoll  rc522.o
    0x08001ce4   0x08001ce4   0x0000005e   Code   RO         2732    i.MFRC522_CalculateCRC  rc522.o
    0x08001d42   0x08001d42   0x00000020   Code   RO         2733    i.MFRC522_Check     rc522.o
    0x08001d62   0x08001d62   0x00000018   Code   RO         2734    i.MFRC522_ClearBitMask  rc522.o
    0x08001d7a   0x08001d7a   0x0000001c   Code   RO         2736    i.MFRC522_Compare   rc522.o
    0x08001d96   0x08001d96   0x0000002c   Code   RO         2738    i.MFRC522_Halt      rc522.o
    0x08001dc2   0x08001dc2   0x0000005e   Code   RO         2739    i.MFRC522_Init      rc522.o
    0x08001e20   0x08001e20   0x0000000e   Code   RO         2741    i.MFRC522_ReadRegister  rc522.o
    0x08001e2e   0x08001e2e   0x0000002e   Code   RO         2742    i.MFRC522_Request   rc522.o
    0x08001e5c   0x08001e5c   0x00000008   Code   RO         2743    i.MFRC522_Reset     rc522.o
    0x08001e64   0x08001e64   0x00000052   Code   RO         2744    i.MFRC522_SelectTag  rc522.o
    0x08001eb6   0x08001eb6   0x00000018   Code   RO         2745    i.MFRC522_SetBitMask  rc522.o
    0x08001ece   0x08001ece   0x00000114   Code   RO         2746    i.MFRC522_ToCard    rc522.o
    0x08001fe2   0x08001fe2   0x0000000a   Code   RO         2748    i.MFRC522_WriteRegister  rc522.o
    0x08001fec   0x08001fec   0x00000140   Code   RO          181    i.MX_GPIO_Init      gpio.o
    0x0800212c   0x0800212c   0x00000048   Code   RO          207    i.MX_SPI1_Init      spi.o
    0x08002174   0x08002174   0x00000038   Code   RO          251    i.MX_UART4_Init     usart.o
    0x080021ac   0x080021ac   0x00000038   Code   RO          252    i.MX_USART1_UART_Init  usart.o
    0x080021e4   0x080021e4   0x00000002   Code   RO          321    i.MemManage_Handler  stm32f1xx_it.o
    0x080021e6   0x080021e6   0x00000002   Code   RO          322    i.NMI_Handler       stm32f1xx_it.o
    0x080021e8   0x080021e8   0x00000006   Code   RO         1490    i.PWR_OverloadWfe   stm32f1xx_hal_pwr.o
    0x080021ee   0x080021ee   0x00000002   Code   RO          323    i.PendSV_Handler    stm32f1xx_it.o
    0x080021f0   0x080021f0   0x00000058   Code   RO          444    i.PowerManager_CheckTimeout  power_manager.o
    0x08002248   0x08002248   0x00000030   Code   RO          445    i.PowerManager_EnterStopMode  power_manager.o
    0x08002278   0x08002278   0x0000000c   Code   RO          446    i.PowerManager_GetState  power_manager.o
    0x08002284   0x08002284   0x0000001c   Code   RO          447    i.PowerManager_HandleRC522Interrupt  power_manager.o
    0x080022a0   0x080022a0   0x00000068   Code   RO          448    i.PowerManager_Init  power_manager.o
    0x08002308   0x08002308   0x000000f8   Code   RO          449    i.PowerManager_PrepareForSleep  power_manager.o
    0x08002400   0x08002400   0x00000174   Code   RO          450    i.PowerManager_RestoreWiFiConnection  power_manager.o
    0x08002574   0x08002574   0x00000010   Code   RO          452    i.PowerManager_UpdateCardDetectTime  power_manager.o
    0x08002584   0x08002584   0x0000011c   Code   RO          453    i.PowerManager_WakeupRestore  power_manager.o
    0x080026a0   0x080026a0   0x00000020   Code   RO         2749    i.SPI1SendByte      rc522.o
    0x080026c0   0x080026c0   0x00000030   Code   RO         2750    i.SPI1_ReadReg      rc522.o
    0x080026f0   0x080026f0   0x00000030   Code   RO         2751    i.SPI1_WriteReg     rc522.o
    0x08002720   0x08002720   0x00000034   Code   RO          608    i.SPI_EndRxTxTransaction  stm32f1xx_hal_spi.o
    0x08002754   0x08002754   0x000000b8   Code   RO          613    i.SPI_WaitFlagStateUntilTimeout  stm32f1xx_hal_spi.o
    0x0800280c   0x0800280c   0x00000002   Code   RO          324    i.SVC_Handler       stm32f1xx_it.o
    0x0800280e   0x0800280e   0x00000002   PAD
    0x08002810   0x08002810   0x00000048   Code   RO         2418    i.String_Index      bsp_lcd_ili9341.o
    0x08002858   0x08002858   0x00000004   Code   RO          325    i.SysTick_Handler   stm32f1xx_it.o
    0x0800285c   0x0800285c   0x0000005e   Code   RO           14    i.SystemClock_Config  main.o
    0x080028ba   0x080028ba   0x00000002   Code   RO         2258    i.SystemInit        system_stm32f1xx.o
    0x080028bc   0x080028bc   0x00000010   Code   RO         1937    i.UART_DMAAbortOnError  stm32f1xx_hal_uart.o
    0x080028cc   0x080028cc   0x0000004e   Code   RO         1947    i.UART_EndRxTransfer  stm32f1xx_hal_uart.o
    0x0800291a   0x0800291a   0x000000c2   Code   RO         1949    i.UART_Receive_IT   stm32f1xx_hal_uart.o
    0x080029dc   0x080029dc   0x000000b8   Code   RO         1950    i.UART_SetConfig    stm32f1xx_hal_uart.o
    0x08002a94   0x08002a94   0x00000036   Code   RO         1952    i.UART_Start_Receive_IT  stm32f1xx_hal_uart.o
    0x08002aca   0x08002aca   0x00000072   Code   RO         1953    i.UART_WaitOnFlagUntilTimeout  stm32f1xx_hal_uart.o
    0x08002b3c   0x08002b3c   0x0000000c   Code   RO          326    i.USART1_IRQHandler  stm32f1xx_it.o
    0x08002b48   0x08002b48   0x00000002   Code   RO          327    i.UsageFault_Handler  stm32f1xx_it.o
    0x08002b4a   0x08002b4a   0x00000002   PAD
    0x08002b4c   0x08002b4c   0x00000058   Code   RO         2673    i.WIFI_TCP_SendData  wifi.o
    0x08002ba4   0x08002ba4   0x00000020   Code   RO         3103    i.__0printf$8       mc_w.l(printf8.o)
    0x08002bc4   0x08002bc4   0x00000028   Code   RO         3105    i.__0sprintf$8      mc_w.l(printf8.o)
    0x08002bec   0x08002bec   0x00000020   Code   RO         1371    i.__NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x08002c0c   0x08002c0c   0x0000000e   Code   RO         3216    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08002c1a   0x08002c1a   0x00000002   Code   RO         3217    i.__scatterload_null  mc_w.l(handlers.o)
    0x08002c1c   0x08002c1c   0x0000000e   Code   RO         3218    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08002c2a   0x08002c2a   0x00000002   PAD
    0x08002c2c   0x08002c2c   0x00000410   Code   RO         3110    i._printf_core      mc_w.l(printf8.o)
    0x0800303c   0x0800303c   0x00000024   Code   RO         3111    i._printf_post_padding  mc_w.l(printf8.o)
    0x08003060   0x08003060   0x0000002e   Code   RO         3112    i._printf_pre_padding  mc_w.l(printf8.o)
    0x0800308e   0x0800308e   0x0000000a   Code   RO         3114    i._sputc            mc_w.l(printf8.o)
    0x08003098   0x08003098   0x00000050   Code   RO         2605    i.connectTcpServer  esp32.o
    0x080030e8   0x080030e8   0x00000048   Code   RO         2606    i.connectWiFi       esp32.o
    0x08003130   0x08003130   0x0000001c   Code   RO         2419    i.delay_ms          bsp_lcd_ili9341.o
    0x0800314c   0x0800314c   0x000000e4   Code   RO         2420    i.drawAscii         bsp_lcd_ili9341.o
    0x08003230   0x08003230   0x00000018   Code   RO          253    i.fputc             usart.o
    0x08003248   0x08003248   0x000006f4   Code   RO           15    i.main              main.o
    0x0800393c   0x0800393c   0x00000048   Code   RO         2422    i.readData          bsp_lcd_ili9341.o
    0x08003984   0x08003984   0x00000074   Code   RO         2607    i.sendData          esp32.o
    0x080039f8   0x080039f8   0x00000024   Code   RO         2424    i.sendDataShort     bsp_lcd_ili9341.o
    0x08003a1c   0x08003a1c   0x00000024   Code   RO         2425    i.sendOrder         bsp_lcd_ili9341.o
    0x08003a40   0x08003a40   0x0000003c   Code   RO         2608    i.setATMode         esp32.o
    0x08003a7c   0x08003a7c   0x00000088   Code   RO         2426    i.setCursor         bsp_lcd_ili9341.o
    0x08003b04   0x08003b04   0x00000012   Data   RO         1069    .constdata          stm32f1xx_hal_rcc.o
    0x08003b16   0x08003b16   0x00000010   Data   RO         2259    .constdata          system_stm32f1xx.o
    0x08003b26   0x08003b26   0x00000008   Data   RO         2260    .constdata          system_stm32f1xx.o
    0x08003b2e   0x08003b2e   0x00003348   Data   RO         2428    .constdata          bsp_lcd_ili9341.o
    0x08006e76   0x08006e76   0x00000002   PAD
    0x08006e78   0x08006e78   0x00000020   Data   RO         3214    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08006e98, Size: 0x00000dc0, Max: 0x0000c000, ABSOLUTE, COMPRESSED[0x00000098])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x0000040d   Data   RW           18    .data               main.o
    0x2000040d   COMPRESSED   0x00000001   PAD
    0x2000040e   COMPRESSED   0x00000004   Data   RW          255    .data               usart.o
    0x20000412   COMPRESSED   0x00000002   PAD
    0x20000414   COMPRESSED   0x0000000c   Data   RW          454    .data               power_manager.o
    0x20000420   COMPRESSED   0x0000000c   Data   RW          906    .data               stm32f1xx_hal.o
    0x2000042c   COMPRESSED   0x00000004   Data   RW         2261    .data               system_stm32f1xx.o
    0x20000430   COMPRESSED   0x00000003   Data   RW         2354    .data               key.o
    0x20000433   COMPRESSED   0x00000001   PAD
    0x20000434   COMPRESSED   0x00000090   Data   RW         2430    .data               bsp_lcd_ili9341.o
    0x200004c4   COMPRESSED   0x00000004   Data   RW         3177    .data               mc_w.l(stdout.o)
    0x200004c8        -       0x00000058   Zero   RW          208    .bss                spi.o
    0x20000520        -       0x00000490   Zero   RW          254    .bss                usart.o
    0x200009b0        -       0x0000000a   Zero   RW         2427    .bss                bsp_lcd_ili9341.o
    0x200009ba   COMPRESSED   0x00000006   PAD
    0x200009c0        -       0x00000400   Zero   RW            1    STACK               startup_stm32f103xe.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      2072        138      13128        144         10      11843   bsp_lcd_ili9341.o
       448        146          0          0          0       4533   esp32.o
       320         24          0          0          0       1023   gpio.o
        80         10          0          3          0       1631   key.o
      1878        766          0       1037          0     466039   main.o
      1200        552          0         12          0       5891   power_manager.o
       996         12          0          0          0      11322   rc522.o
       184         24          0          0         88       1609   spi.o
        36          8        304          0       1024        792   startup_stm32f103xe.o
       164         28          0         12          0       6433   stm32f1xx_hal.o
       198         14          0          0          0      28875   stm32f1xx_hal_cortex.o
       374          8          0          0          0       1803   stm32f1xx_hal_dma.o
       548         48          0          0          0       4070   stm32f1xx_hal_gpio.o
        60          8          0          0          0        850   stm32f1xx_hal_msp.o
        74          8          0          0          0       1092   stm32f1xx_hal_pwr.o
      1240         84         18          0          0       5052   stm32f1xx_hal_rcc.o
       910          4          0          0          0       4617   stm32f1xx_hal_spi.o
      1806         10          0          0          0      12256   stm32f1xx_hal_uart.o
        56          6          0          0          0       5898   stm32f1xx_it.o
         2          0         24          4          0       1083   system_stm32f1xx.o
       432         68          0          4       1168       3903   usart.o
        88         28          0          0          0       1096   wifi.o

    ----------------------------------------------------------------------
     13180       <USER>      <GROUP>       1220       2296     581711   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        14          0          2          4          6          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        86          0          0          0          0          0   __dczerorl2.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        30          0          0          0          0         68   llshl.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0        108   memseta.o
      1204         60          0          0          0        504   printf8.o
         0          0          0          4          0          0   stdout.o
        14          0          0          0          0         68   strlen.o
        36          0          0          0          0         80   strstr.o
        98          0          0          0          0         92   uldiv.o

    ----------------------------------------------------------------------
      1624         <USER>          <GROUP>          4          0       1056   Library Totals
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1622         76          0          4          0       1056   mc_w.l

    ----------------------------------------------------------------------
      1624         <USER>          <GROUP>          4          0       1056   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     14804       2070      13508       1224       2296     576603   Grand Totals
     14804       2070      13508        152       2296     576603   ELF Image Totals (compressed)
     14804       2070      13508        152          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                28312 (  27.65kB)
    Total RW  Size (RW Data + ZI Data)              3520 (   3.44kB)
    Total ROM Size (Code + RO Data + RW Data)      28464 (  27.80kB)

==============================================================================

